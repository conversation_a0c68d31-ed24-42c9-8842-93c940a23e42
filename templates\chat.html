<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-blue-rgb: 0, 74, 173; /* RGB values for primary blue */
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --navbar-height: 5.5rem;
            --navbar-height-mobile: 4.5rem;
            --contacts-width: 400px;
            --contacts-width-mobile: 100%;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--dark-color);
            background-color: #f9fafb;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Navbar Styles - Matching client_page.html */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: var(--navbar-height);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 74, 173, 0.08);
        }

        .mobile-menu-btn {
            display: none;
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            z-index: 1001;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-right: 1rem;
            text-decoration: none;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            margin-left: -0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: color 0.3s ease;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1002;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon - Matching genius_page.html */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Notification container to properly position dropdown */
        .notification-container {
            position: relative;
            display: inline-block;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.4rem;
            color: var(--primary-blue);
            transition: color 0.3s ease;
        }

        .notification-icon:hover i {
            color: var(--primary-pink);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 50%;
            min-width: 18px;
            height: 18px;
            font-size: 0.7rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            line-height: 1;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }

        .notification-badge.new {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes bounce {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
            20%, 40%, 60%, 80% { transform: translateX(3px); }
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-button {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
            transition: border-color 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            max-width: 52px;
            max-height: 52px;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1002;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Professional Notification dropdown - Matching genius_page.html exactly */
        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(0, 74, 173, 0.08);
            border-radius: 16px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.08),
                0 8px 16px rgba(0, 0, 0, 0.04),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            width: 380px;
            max-height: 480px;
            overflow: hidden;
            z-index: 1001;
            display: none;
            margin-top: 12px;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 576px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -100px;
                max-height: 400px;
            }
        }

        @media (max-width: 480px) {
            .notification-dropdown {
                right: -150px;
            }
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.3;
            color: var(--primary-blue);
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-notifications h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
        }

        .empty-notifications p {
            font-size: 0.9rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        /* Professional Notification items - Matching genius_page.html exactly */
        .notification-item {
            padding: 12px 20px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.04);
            cursor: pointer;
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            display: flex;
            align-items: flex-start;
        }

        .notification-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-item:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.08);
        }

        .notification-item:hover::before {
            opacity: 1;
        }

        .notification-item:last-child {
            border-bottom: none;
            border-radius: 0 0 16px 16px;
        }

        .notification-item:first-of-type {
            border-radius: 0;
        }

        .notification-content {
            flex: 1;
            cursor: pointer;
        }

        .notification-content p {
            margin: 0 0 6px 0;
            line-height: 1.4;
            color: var(--neutral-800);
            font-size: 0.9rem;
            cursor: pointer;
        }

        .notification-content p strong {
            color: var(--neutral-900);
            font-weight: 600;
        }

        .notification-content span {
            font-size: 0.75rem;
            color: var(--neutral-600);
            display: block;
        }

        .notification-title {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--primary-blue);
            line-height: 1.4;
            flex: 1;
            letter-spacing: -0.01em;
        }

        .notification-text {
            font-size: 0.85rem;
            color: #1f2937;
            margin: 0px;
            line-height: 1.3;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .notification-time {
            font-size: 0.7rem;
            color: #6b7280;
            margin-top: 0px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .notification-time::before {
            content: '•';
            color: var(--primary-pink);
            font-weight: bold;
        }

        .notification-item.unread {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.05) 0%, rgba(205, 32, 139, 0.02) 100%);
        }

        .notification-item.unread:hover {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.08) 0%, rgba(205, 32, 139, 0.04) 100%);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.12);
        }

        .notification-icon-wrapper {
            margin-right: 15px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light-blue), var(--primary-light-pink));
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            cursor: pointer;
        }

        .notification-item i {
            color: var(--primary-blue);
            font-size: 1.2rem;
        }

        .notification-actions {
            display: flex;
            margin-top: 12px;
            gap: 10px;
        }

        .accept-btn, .reject-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .accept-btn {
            background-color: var(--primary-blue);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 74, 173, 0.2);
        }

        .accept-btn:hover {
            background-color: #003d8f;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 74, 173, 0.3);
        }

        .reject-btn {
            background-color: white;
            color: var(--neutral-700);
            border: 1px solid var(--neutral-400);
        }

        .reject-btn:hover {
            background-color: var(--neutral-200);
            color: var(--neutral-900);
        }

        .notification-status {
            font-size: 0.75rem;
            padding: 4px 10px;
            border-radius: 20px;
            margin-top: 8px;
            display: inline-block;
            font-weight: 500;
        }

        .notification-status.accepted {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .notification-status.rejected {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.3;
            color: var(--primary-blue);
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-notifications h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
        }

        .empty-notifications p {
            font-size: 0.9rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        /* Professional scrollbar for notifications */
        .notification-dropdown {
            overflow-y: auto;
        }

        .notification-dropdown::-webkit-scrollbar {
            width: 6px;
        }

        .notification-dropdown::-webkit-scrollbar-track {
            background: rgba(0, 74, 173, 0.02);
            border-radius: 3px;
        }

        .notification-dropdown::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .notification-dropdown::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--primary-blue) 100%);
            transform: scale(1.1);
        }

        /* Notification list container */
        #notification-list {
            max-height: 400px;
            overflow-y: auto;
        }

        /* Unified Notification System Styles - Matching genius_page.html */
        .unified-notification {
            position: fixed;
            padding: 18px 24px;
            border-radius: 16px;
            color: white;
            font-weight: 500;
            font-size: 0.95rem;
            min-width: 320px;
            max-width: 450px;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 15px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(205, 32, 139, 0.95));
            cursor: pointer;
        }

        .unified-notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        .unified-notification.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(0, 148, 133, 0.95));
            border-left-color: #10b981;
        }

        .unified-notification.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
            border-left-color: #ef4444;
        }

        .unified-notification.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.95), rgba(217, 119, 6, 0.95));
            border-left-color: #f59e0b;
        }

        .unified-notification.info {
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(59, 130, 246, 0.95));
            border-left-color: var(--primary-blue);
        }

        .unified-notification.application {
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.95), rgba(160, 82, 45, 0.95));
            border-left-color: #8b4513;
        }

        .unified-notification.contract {
            background: linear-gradient(135deg, rgba(75, 0, 130, 0.95), rgba(138, 43, 226, 0.95));
            border-left-color: #4b0082;
        }

        .unified-notification .notification-icon {
            margin-right: 15px;
            font-size: 1.3rem;
            flex-shrink: 0;
        }

        .unified-notification .notification-content {
            flex: 1;
        }

        .unified-notification .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 1rem;
        }

        .unified-notification .notification-message {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .unified-notification .notification-close {
            background: none;
            border: none;
            color: white;
            opacity: 0.7;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 4px;
            margin-left: 10px;
            transition: all 0.3s ease;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .unified-notification .notification-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }



        /* Main content */
        .main-content {
            padding: var(--navbar-height) 0 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            display: flex;
            height: calc(100vh - var(--navbar-height));
            width: 100%;
            background-color: #fff;
            position: relative;
        }

        /* Toggle button for mobile */
        .toggle-contacts-btn {
            display: none;
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 100;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        /* Left sidebar - Contacts list */
        .contacts-list {
            width: var(--contacts-width);
            border-right: 1px solid #e5e7eb;
            background-color: white;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
            z-index: 10;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
        }

        .contacts-header {
            padding: 20px 20px 18px 20px;
            border-bottom: 1px solid #e5e7eb;
            background-color: #fff;
            font-weight: 700;
            font-size: 1.5rem;
            color: #1c1e21;
        }

        .contacts-search {
            position: relative;
            margin: 16px 16px 12px 16px;
        }

        .contacts-search input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid #e5e7eb;
            border-radius: 20px;
            font-size: 0.9rem;
            background-color: #f0f2f5;
            transition: all 0.2s ease;
        }

        .contacts-search input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background-color: #fff;
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .contacts-search i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #65676b;
            font-size: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .contact-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 92px; /* Start after avatar + margin */
            right: 20px;
            height: 1px;
            background-color: #e5e7eb;
        }

        .contact-item:last-child::after {
            display: none; /* Remove line from last item */
        }

        .contact-item:hover {
            background-color: #f8fafc;
        }

        .contact-item:hover::after {
            background-color: #d1d5db; /* Slightly darker line on hover */
        }

        .contact-item.active {
            background-color: #f0f7ff;
            border-left: 4px solid var(--primary-blue);
        }

        .contact-item.active::after {
            background-color: #bfdbfe; /* Lighter blue line for active item */
        }

        .contact-item.active:hover {
            background-color: #e6f3ff;
        }

        .contact-avatar {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            margin-right: 16px;
            object-fit: cover;
            border: 2px solid #f0f0f0;
            background-color: var(--primary-blue);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.3rem;
            position: relative;
        }

        /* Online status indicator */
        .contact-avatar::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            background-color: #9ca3af; /* Default offline color */
            transition: background-color 0.3s ease;
        }

        .contact-avatar.online::after {
            background-color: #10b981; /* Green for online */
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
        }

        .contact-avatar.away::after {
            background-color: #f59e0b; /* Yellow for away */
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
        }

        .contact-avatar.offline::after {
            background-color: #9ca3af; /* Gray for offline */
        }

        /* Status text colors */
        .status-online {
            color: #10b981 !important;
        }

        .status-away {
            color: #f59e0b !important;
        }

        .status-offline {
            color: #9ca3af !important;
        }

        .contact-info {
            flex: 1;
        }

        .contact-name {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 1rem;
            line-height: 1.3;
            color: #1c1e21;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #9ca3af;
            flex-shrink: 0;
            transition: background-color 0.3s ease;
        }

        .contact-status-indicator.online {
            background-color: #10b981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
        }

        .contact-status-indicator.away {
            background-color: #f59e0b;
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
        }

        .contact-status-indicator.offline {
            background-color: #9ca3af;
        }

        .contact-preview {
            color: #65676b;
            font-size: 0.9rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 220px;
            line-height: 1.4;
        }

        .contact-meta {
            text-align: right;
            margin-left: 12px;
        }

        .contact-time {
            font-size: 0.8rem;
            color: #65676b;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .contact-badge, .unread-badge {
            display: inline-block;
            background-color: var(--primary-blue);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            padding: 2px 6px;
            border-radius: 10px;
        }

        .unread-badge {
            background-color: #ef4444;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f9fafb;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .chat-header {
            padding: 15px 20px;
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            z-index: 10;
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .chat-header-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-info-btn {
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-info-btn:hover {
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-pink);
        }

        /* Chat Info Sidebar */
        .chat-info-sidebar {
            position: fixed;
            top: var(--navbar-height);
            right: -400px;
            width: 400px;
            height: calc(100vh - var(--navbar-height));
            background: white;
            color: #333;
            z-index: 999;
            transition: right 0.3s ease;
            overflow-y: auto;
            box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
            font-family: 'Poppins', sans-serif;
            border-left: 1px solid #e5e7eb;
        }

        .chat-info-sidebar.active {
            right: 0;
        }

        .chat-info-header {
            padding: 30px 20px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .chat-info-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 15px;
            object-fit: cover;
            border: 3px solid var(--primary-blue);
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
        }

        .chat-info-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
            font-family: 'Poppins', sans-serif;
            color: #1f2937;
        }

        .chat-info-status {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            color: #6b7280;
        }

        .chat-info-actions {
            display: flex;
            justify-content: center;
            gap: 25px;
            padding: 25px 20px;
            border-bottom: 1px solid #e5e7eb;
            background: white;
        }

        .chat-action-btn {
            background: var(--primary-blue);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 55px;
            height: 55px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
        }

        .chat-action-btn:hover {
            background: var(--primary-pink);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
        }

        .chat-action-label {
            font-size: 0.85rem;
            margin-top: 8px;
            text-align: center;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
            color: #6b7280;
        }

        .chat-info-sections {
            padding: 0;
        }

        .chat-info-section {
            border-bottom: 1px solid #e5e7eb;
        }

        .chat-section-header {
            padding: 18px 20px;
            background: #f9fafb;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .chat-section-header:hover {
            background: #f3f4f6;
            transform: translateX(5px);
        }

        .chat-section-title {
            font-weight: 600;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            color: #1f2937;
        }

        .chat-section-content {
            padding: 0 20px 15px;
            display: none;
            background: white;
        }

        .chat-section-content.active {
            display: block;
        }

        .chat-section-item {
            padding: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 5px 0;
            padding-left: 10px;
            color: #4b5563;
        }

        .chat-section-item:hover {
            background: #f3f4f6;
            transform: translateX(5px);
            color: var(--primary-blue);
        }

        .chat-section-item i {
            width: 20px;
            text-align: center;
            color: var(--primary-blue);
            font-size: 1.1rem;
        }

        .chat-info-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #f3f4f6;
            border: none;
            color: #6b7280;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .chat-info-close:hover {
            background: var(--primary-pink);
            color: white;
            transform: scale(1.1);
            border-color: var(--primary-pink);
        }

        .chat-messages {
            position: absolute;
            top: 70px;
            bottom: 70px;
            left: 0;
            right: 0;
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .message {
            margin-bottom: 15px;
            max-width: 70%;
            position: relative;
            clear: both;
            overflow: visible;
            padding: 0 40px; /* Increased padding for reaction buttons */
        }

        .message-outgoing {
            float: right;
            margin-left: auto;
            margin-bottom: 8px;
            clear: both;
        }

        .message-outgoing .message-bubble {
            margin-right: 8px;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
            word-break: break-word;
            display: inline-block;
            max-width: 100%;
            line-height: 1.4;
            font-size: 0.95rem;
        }

        .message-incoming {
            float: left;
            margin-right: auto;
            margin-bottom: 8px;
            clear: both;
        }

        .message-incoming .message-bubble {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
            color: #1f2937;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-left: 8px;
        }

        .message-outgoing .message-bubble {
            background: linear-gradient(135deg, #004AAD 0%, #0056CC 100%);
            color: #ffffff;
            border-bottom-right-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .auto-message {
            background-color: var(--primary-pink) !important;
        }

        .message-time {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 4px;
            display: block;
            text-align: right;
            clear: both;
            font-weight: 500;
        }

        .message-incoming .message-time {
            text-align: left;
            color: #6b7280;
        }

        .message-outgoing .message-time {
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            font-weight: 500;
        }

        /* Message reactions styles */
        .message-reactions {
            display: flex !important;
            flex-wrap: wrap;
            gap: 2px;
            margin-top: 3px;
            max-width: 100%;
            position: relative;
            z-index: 5;
            min-height: 22px; /* Ensure there's space for reactions */
            padding: 2px 4px;
            border-radius: 8px;
            background-color: rgba(240, 240, 240, 0.5);
            cursor: pointer;
            visibility: visible !important;
            opacity: 1 !important;
            /* Always keep the container visible */
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Make sure reaction items are always visible */
        .reaction-item {
            display: inline-flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .message-reactions:empty::after {
            content: "";
            min-height: 22px;
            display: block;
            width: 100%;
        }

        @keyframes pulse {
            0% { opacity: 0.4; }
            50% { opacity: 0.8; }
            100% { opacity: 0.4; }
        }

        .message-outgoing .message-reactions {
            justify-content: flex-end;
            margin-right: 8px;
            padding-right: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-incoming .message-reactions {
            justify-content: flex-start;
            margin-left: 3px;
            padding-left: 5px;
        }

        .reaction-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            padding: 3px;
            opacity: 0; /* Hidden by default */
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f0f0f0; /* Light gray background */
            border: 1px solid #ddd; /* Light border */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            z-index: 10; /* Ensure it's above other elements */
            color: #666; /* Darker text color */
            transition: opacity 0.2s ease; /* Smooth transition */
        }

        .message-outgoing .reaction-button {
            left: 3px;
        }

        .message-incoming .reaction-button {
            right: 3px;
        }

        /* Show reaction button on message hover */
        .message:hover .reaction-button {
            opacity: 1; /* Show on message hover */
        }

        /* Add hover effect for the button itself */
        .reaction-button:hover {
            background-color: #e0e0e0;
            color: #333;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transform: translateY(-50%) scale(1.1);
        }

        .reaction-item {
            display: inline-flex !important;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 8px;
            padding: 3px 6px;
            margin-right: 3px;
            margin-bottom: 3px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #ccc;
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
            opacity: 1 !important;
            visibility: visible !important;
        }

        .reaction-item:hover {
            background-color: #e0e0e0;
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .reaction-item.active {
            background-color: rgba(0, 74, 173, 0.2);
            border-color: rgba(0, 74, 173, 0.4);
            color: #004aad;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0, 74, 173, 0.3);
        }

        .reaction-emoji {
            margin-right: 3px;
            font-size: 1rem;
        }

        .reaction-count {
            font-size: 0.8rem;
            font-weight: 600;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 6px;
            padding: 1px 4px;
            min-width: 16px;
            text-align: center;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .reaction-time {
            font-size: 0.65rem;
            color: #777;
            margin-left: 3px;
            opacity: 0.8;
            display: none; /* Hidden by default */
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 10;
            pointer-events: none; /* Prevents the tooltip from interfering with mouse events */
        }

        .reaction-item {
            position: relative; /* Needed for absolute positioning of the tooltip */
        }

        .reaction-item:hover .reaction-time {
            display: block; /* Show on hover */
        }

        /* Animation for reaction highlight */
        @keyframes reaction-pulse {
            0% { box-shadow: 0 0 0 0 rgba(var(--primary-blue-rgb), 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(var(--primary-blue-rgb), 0); }
            100% { box-shadow: 0 0 0 0 rgba(var(--primary-blue-rgb), 0); }
        }

        .reaction-highlight {
            animation: reaction-pulse 1s ease-out;
        }

        /* Animation for new reaction items */
        @keyframes reaction-pop {
            0% { transform: scale(0.5); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        .reaction-item-new {
            animation: reaction-pop 0.5s ease;
        }

        .reaction-picker {
            position: absolute;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.3);
            padding: 8px;
            display: none;
            z-index: 100;
            top: 50%;
            transform: translateY(-50%);
            border: 1px solid #ccc;
            width: 220px;
        }

        .message-outgoing .reaction-picker {
            left: -60px;
        }

        .message-incoming .reaction-picker {
            right: -60px;
        }

        .reaction-picker button {
            background: #f5f5f5;
            border: 1px solid #ddd;
            font-size: 1.2rem;
            padding: 5px;
            cursor: pointer;
            margin: 0 3px;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .reaction-picker button:hover {
            transform: scale(1.1);
            background-color: #e0e0e0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .message-status {
            display: inline-flex;
            align-items: center;
            margin-left: 6px;
        }

        .message-status i {
            font-size: 0.85rem;
            margin-left: 4px;
            display: inline-block;
            width: 14px;
            height: 14px;
            text-align: center;
            opacity: 0.9;
        }

        .message-outgoing .message-status i {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .message-incoming .message-status i {
            color: #6b7280;
        }

        /* System messages and loading indicators */
        .system-message {
            text-align: center;
            margin: 15px 0;
            clear: both;
            width: 100%;
        }

        .system-message span {
            display: inline-block;
            padding: 5px 15px;
            background-color: #f3f4f6;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #6b7280;
        }

        #loading-indicator {
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 10px auto;
            font-size: 0.9rem;
            color: var(--primary-blue);
            position: sticky;
            top: 0;
            z-index: 5;
        }

        #load-more-messages {
            margin: 10px 0;
            text-align: center;
        }

        #load-more-messages button {
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: var(--primary-blue);
            transition: all 0.2s ease;
        }

        #load-more-messages button:hover {
            background-color: #e5e7eb;
        }

        .chat-input {
            padding: 15px 20px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            z-index: 10;
        }

        .input-group {
            position: relative;
        }

        .form-control {
            padding: 12px 15px;
            border-radius: 24px;
            border: 1px solid #e5e7eb;
            padding-right: 50px;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: var(--primary-color);
        }

        .send-button {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            background-color: var(--secondary-color);
        }

        .empty-chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6b7280;
            text-align: center;
            padding: 2rem;
        }

        .empty-chat i {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        .empty-chat p {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .empty-chat span {
            font-size: 0.9rem;
            color: #9ca3af;
        }

        .system-message {
            text-align: center;
            margin: 20px 0;
            color: #6b7280;
            font-size: 0.9rem;
            clear: both;
        }

        .system-message span {
            background-color: #f3f4f6;
            padding: 5px 10px;
            border-radius: 10px;
        }

        .auto-message {
            background-color: #fff8e6 !important;
            border: 1px solid #fde68a !important;
        }

        .client-message {
            border-left: 3px solid #3b82f6 !important;
        }

        .genius-message {
            border-left: 3px solid #10b981 !important;
        }

        .message-image {
            background-color: white;
            padding: 8px;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .message-image img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
        }

        .message-outgoing .message-image {
            border: 1px solid #e5e7eb;
        }

        .message-image .small {
            color: #6b7280 !important;
        }

        .message-file {
            padding: 8px;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .message-file a {
            color: #333 !important;
        }

        /* File upload styles */
        .file-upload-btn {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .file-upload-btn:hover:not(:disabled) {
            opacity: 1;
        }

        .file-upload-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        #file-preview-container {
            background-color: #f9fafb;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Style for message input when file is selected */
        .file-selected {
            background-color: #f0f7ff !important;
            border-color: var(--primary-blue) !important;
            font-style: italic;
            color: #666;
        }

        .message-reply {
            border-left: 3px solid #6b7280;
            padding-left: 8px !important;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .mobile-menu-btn {
                display: block;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: var(--navbar-height-mobile);
                left: 0;
                right: 0;
                background: white;
                flex-direction: column;
                padding: 0;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 998;
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links a, .nav-dropdown {
                width: 100%;
                padding: 15px;
                border-bottom: 1px solid #eee;
            }

            .nav-dropdown-content {
                position: static;
                box-shadow: none;
                display: none;
                width: 100%;
                padding-left: 15px;
            }

            .nav-dropdown.active .nav-dropdown-content {
                display: block;
            }

            .nav-dropbtn {
                width: 100%;
                text-align: left;
                justify-content: space-between;
            }

            .logo {
                margin-right: 0;
            }

            .navbar {
                height: var(--navbar-height-mobile);
                padding: 0 0.5rem;
            }

            .main-content {
                padding-top: var(--navbar-height-mobile);
            }

            .chat-container {
                height: calc(100vh - var(--navbar-height-mobile));
            }

            .toggle-contacts-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .contacts-list {
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: var(--contacts-width-mobile);
                transform: translateX(-100%);
                z-index: 99;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .contacts-list.active {
                transform: translateX(0);
            }

            .chat-header {
                padding-left: 60px;
            }

            .message {
                max-width: 85%;
            }
        }

        @media (max-width: 576px) {
            .navbar {
                padding: 0 0.5rem;
            }

            .logo img {
                width: 3.5rem;
                height: 3.5rem;
            }

            .logo h1 {
                font-size: 1.3rem;
            }

            .profile-button {
                width: 40px;
                height: 40px;
            }

            .notification-icon i {
                font-size: 1.5rem;
            }

            .right-section {
                gap: 1rem;
            }

            .message {
                max-width: 90%;
            }

            .chat-messages {
                padding: 15px 10px;
            }

            .chat-input {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar - Matching client_page.html -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
            </a>
            <button class="mobile-menu-btn" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-links" id="navLinks">
                <!-- Desktop Navigation Links -->
                {% if session.get('user_type') == 'genius' %}
                <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                <a href="#" id="myApplicationsLink">My Applications</a>

                <!-- Contracts Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Contracts
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Log Hours</a>
                        <a href="{{ url_for('landing_page') }}">Work Diary</a>
                    </div>
                </div>

                <!-- Earnings Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Earnings
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                        <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                        <a href="{{ url_for('landing_page') }}">Tax Info</a>
                    </div>
                </div>
                {% else %}
                <a href="{{ url_for('page1') }}">Post a Gig</a>

                <!-- Desktop Overview Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Overview
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('allgigpost') }}">All gig posts</a>
                        <a href="{{ url_for('all_contracts') }}">All contracts</a>
                        <a href="{{ url_for('your_hires') }}">Your Hires</a>
                    </div>
                </div>

                <!-- Desktop Manage Work Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Manage Work
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Timesheet</a>
                        <a href="{{ url_for('landing_page') }}">Invoices</a>
                    </div>
                </div>

                <!-- Desktop Reports Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Reports
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                        <a href="{{ url_for('landing_page') }}">Transaction History</a>
                    </div>
                </div>
                {% endif %}

                <a href="{{ url_for('messages') }}" class="active">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="auth-buttons">
                <div class="notification-container">
                    <div class="notification-icon" id="notification-bell">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count" class="notification-badge" style="display: none;">0</span>
                    </div>
                    <div class="notification-dropdown">
                        <div class="notification-header">
                            <span>Notifications</span>
                            <span class="notification-header-actions" id="mark-all-read">Mark all as read</span>
                        </div>
                        <div id="notification-list">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div id="empty-notifications" class="empty-notifications" style="display: none;">
                            <i class="far fa-bell-slash"></i>
                            <h3>No notifications yet</h3>
                            <p>You'll see application updates and important notifications here</p>
                        </div>
                    </div>
                </div>
                <div class="profile-dropdown">
                    <div class="profile-button">
                        <img src="{{ url_for('api_profile_photo', user_type=session.get('user_type'), user_id=session.get('user_id')) }}" alt="Profile Picture">
                    </div>
                    <div class="profile-dropdown-content">
                        {% if session.get('user_type') == 'genius' %}
                        <a href="{{ url_for('genius_page') }}">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        {% else %}
                        <a href="{{ url_for('client_page') }}">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        {% endif %}
                        <a href="{{ url_for('landing_page') }}">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{{ url_for('logout') }}" class="logout-option">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="chat-container">
            <!-- Toggle button for mobile -->
            <button id="toggle-contacts-btn" class="toggle-contacts-btn">
                <i class="fas fa-bars"></i>
            </button>

            <div class="contacts-list" id="contacts-list">
                <div class="contacts-header">
                    <h5 class="mb-0">Messages</h5>
                </div>
                <div class="contacts-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search contacts...">
                </div>
                <div id="contacts-container">
                    <!-- Contacts will be loaded here -->
                </div>
            </div>

            <div class="chat-area">
                <div id="chat-header" class="chat-header">
                    <div class="chat-header-left">
                        <button id="toggle-contacts-btn" class="btn btn-sm d-lg-none me-2">
                            <i class="fas fa-bars"></i>
                        </button>
                        <img src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="User" class="contact-avatar">
                        <div class="contact-info">
                            <div id="contact-name">Select a conversation</div>
                            <div id="contact-status" class="small text-muted">
                                Online
                            </div>
                        </div>
                    </div>
                    <div class="chat-header-right">
                        <button id="chat-info-btn" class="chat-info-btn" onclick="toggleChatInfo()">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </div>

                <!-- Empty state (shown by default) -->
                <div id="empty-state" class="chat-messages">
                    <div class="empty-chat">
                        <i class="fas fa-comments"></i>
                        <p>Select a conversation to start messaging</p>
                        <span>Your messages are end-to-end encrypted</span>
                    </div>
                </div>

                <!-- Chat content (hidden by default) -->
                <div id="chat-content" style="display: none;">
                    <div id="chat-messages" class="chat-messages">
                        <!-- Messages will be loaded here -->
                    </div>

                    <div class="chat-input">
                        <div class="chat-input-container" style="display: flex; align-items: center; width: 100%; position: relative;">
                            <!-- File upload button -->
                            <button id="message-file-upload-btn" class="file-upload-btn" disabled style="position: absolute; left: 10px; width: 36px; height: 36px; border-radius: 50%; background: none; color: var(--primary-blue); border: none; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease; outline: none; z-index: 10;">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <input type="file" id="file-input" style="display: none;">

                            <!-- Message input -->
                            <textarea id="message-input" placeholder="Type a message..." rows="1" disabled style="flex: 1; resize: none; border-radius: 18px; padding: 10px 40px 10px 40px; border: 1px solid #e5e7eb; outline: none;"></textarea>
                            <input type="hidden" id="receiver-id" value="">

                            <!-- Send button -->
                            <button id="send-button" onclick="sendMessage()" disabled style="position: absolute; right: 10px; width: 36px; height: 36px; border-radius: 50%; background-color: var(--primary-blue); color: white; border: none; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease; outline: none;">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- File preview container (hidden by default) -->
                        <div id="file-preview-container" style="display: none; padding: 10px; border-top: 1px solid #e5e7eb; background-color: #f9fafb;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center;">
                                    <i class="fas fa-file me-2" id="file-icon"></i>
                                    <span id="file-name">filename.ext</span>
                                </div>
                                <button onclick="cancelFileUpload()" style="background: none; border: none; color: #ef4444; cursor: pointer;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Info Sidebar -->
    <div id="chat-info-sidebar" class="chat-info-sidebar">
        <button class="chat-info-close" onclick="toggleChatInfo()">
            <i class="fas fa-times"></i>
        </button>

        <div class="chat-info-header">
            <img id="chat-info-avatar" src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="User" class="chat-info-avatar">
            <div id="chat-info-name" class="chat-info-name">Contact Name</div>
            <div class="chat-info-status">
                <i class="fas fa-lock"></i>
                End-to-end encrypted
            </div>
        </div>

        <div class="chat-info-actions">
            <div style="text-align: center;">
                <button class="chat-action-btn" onclick="viewProfile()">
                    <i class="fas fa-user"></i>
                </button>
                <div class="chat-action-label">Profile</div>
            </div>
            <div style="text-align: center;">
                <button class="chat-action-btn" onclick="muteChat()">
                    <i class="fas fa-bell-slash"></i>
                </button>
                <div class="chat-action-label">Mute</div>
            </div>
            <div style="text-align: center;">
                <button class="chat-action-btn" onclick="searchInChat()">
                    <i class="fas fa-search"></i>
                </button>
                <div class="chat-action-label">Search</div>
            </div>
        </div>

        <div class="chat-info-sections">
            <div class="chat-info-section">
                <div class="chat-section-header" onclick="toggleSection('chat-info')">
                    <span class="chat-section-title">Chat info</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div id="chat-info-content" class="chat-section-content">
                    <div class="chat-section-item">
                        <i class="fas fa-calendar"></i>
                        <span>Created on January 15, 2024</span>
                    </div>
                </div>
            </div>

            <div class="chat-info-section">
                <div class="chat-section-header" onclick="toggleSection('customize')">
                    <span class="chat-section-title">Customize chat</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div id="customize-content" class="chat-section-content">
                    <div class="chat-section-item">
                        <i class="fas fa-palette"></i>
                        <span>Change theme</span>
                    </div>
                    <div class="chat-section-item">
                        <i class="fas fa-smile"></i>
                        <span>Change emoji</span>
                    </div>
                </div>
            </div>

            <div class="chat-info-section">
                <div class="chat-section-header" onclick="toggleSection('media')">
                    <span class="chat-section-title">Media & files</span>
                    <i class="fas fa-chevron-up"></i>
                </div>
                <div id="media-content" class="chat-section-content active">
                    <div class="chat-section-item">
                        <i class="fas fa-photo-video"></i>
                        <span>Media</span>
                    </div>
                    <div class="chat-section-item">
                        <i class="fas fa-file"></i>
                        <span>Files</span>
                    </div>
                </div>
            </div>

            <div class="chat-info-section">
                <div class="chat-section-header" onclick="toggleSection('privacy')">
                    <span class="chat-section-title">Privacy & support</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div id="privacy-content" class="chat-section-content">
                    <div class="chat-section-item">
                        <i class="fas fa-ban"></i>
                        <span>Block contact</span>
                    </div>
                    <div class="chat-section-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Report contact</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script>
        // Initialize Socket.IO with enhanced configuration for better reliability
        const socket = io({
            path: '/socket.io',
            transports: ['polling', 'websocket'],
            reconnection: true,
            reconnectionAttempts: 10,        // Increased from 5 to 10
            reconnectionDelay: 1000,
            reconnectionDelayMax: 10000,     // Cap at 10 seconds
            timeout: 20000,                  // Increased from 10000 to 20000
            autoConnect: true,
            forceNew: false,
            multiplex: true,
            query: {
                user_id: {{ session.get('user_id', 0) }},
                user_type: '{{ session.get("user_type", "") }}'
            }
        });

        // Log connection details for debugging
        console.log("Socket.IO initialization with URL:", window.location.origin);
        console.log("Socket.IO path:", '/socket.io');
        console.log("Socket.IO user context:", {
            user_id: {{ session.get('user_id', 0) }},
            user_type: '{{ session.get("user_type", "") }}'
        });

        // Track connection status
        let socketConnected = false;
        let messageCheckInterval = null;
        let currentReceiverId = null;
        let conversations = {};
        let contacts = [];
        let onlineUsers = new Set(); // Track online users

        // Add connection error handling with fallback
        socket.on('connect_error', (error) => {
            console.error('Socket.IO connection error:', error);
            socketConnected = false;
            startPollingFallback();
        });

        socket.on('connect_timeout', () => {
            console.error('Socket.IO connection timeout');
            socketConnected = false;
            startPollingFallback();
        });

        socket.on('error', (error) => {
            console.error('Socket.IO error:', error);
            socketConnected = false;
        });

        socket.on('disconnect', (reason) => {
            console.log('Socket.IO disconnected:', reason);
            socketConnected = false;

            // Try to reconnect
            setTimeout(() => {
                if (!socketConnected) {
                    console.log('Attempting to reconnect Socket.IO...');
                    socket.connect();
                }
            }, 2000);

            startPollingFallback();
        });

        // Join user's own room on connect
        socket.on('connect', () => {
            console.log('Connected to Socket.IO');
            socketConnected = true;

            // Join user's room
            socket.emit('join');

            // Request online users list
            socket.emit('get_online_users');

            // Load contacts and conversations
            loadContacts();
        });

        // Fallback polling mechanism for when Socket.IO fails
        function startPollingFallback() {
            if (messageCheckInterval) {
                return; // Already polling
            }

            console.log('Starting message polling fallback mechanism');

            // Check for new messages every 5 seconds
            messageCheckInterval = setInterval(() => {
                if (socketConnected) {
                    stopPollingFallback();
                    return;
                }

                console.log('Polling for new messages...');

                // If we have a current conversation, check for new messages
                if (currentReceiverId) {
                    fetch(`/api/messages/${currentReceiverId}?limit=10&page=1`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.messages && data.messages.length > 0) {
                                // Process new messages
                                const existingMsgIds = new Set(
                                    conversations[currentReceiverId] ?
                                    conversations[currentReceiverId].map(m => m.id) :
                                    []
                                );

                                let hasNewMessages = false;

                                data.messages.forEach(msg => {
                                    // Only add messages we don't already have
                                    if (!existingMsgIds.has(msg.id)) {
                                        hasNewMessages = true;

                                        if (!conversations[currentReceiverId]) {
                                            conversations[currentReceiverId] = [];
                                        }

                                        conversations[currentReceiverId].push({
                                            id: msg.id,
                                            sender_id: msg.sender_id,
                                            sender_type: msg.sender_type,
                                            receiver_id: msg.receiver_id,
                                            receiver_type: msg.receiver_type,
                                            message: msg.message,
                                            timestamp: msg.timestamp || msg.created_at,
                                            is_outgoing: msg.sender_id == {{ session.get('user_id', 0) }},
                                            is_auto: msg.is_auto,
                                            is_read: msg.is_read,
                                            related_to_job_id: msg.related_to_job_id,
                                            related_to_application_id: msg.related_to_application_id,
                                            message_type: msg.message_type,
                                            status: msg.status
                                        });
                                    }
                                });

                                // Update UI if we have new messages
                                if (hasNewMessages) {
                                    displayMessages(currentReceiverId);

                                    // Mark messages as read
                                    fetch(`/api/mark_messages_read/${currentReceiverId}`, {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        }
                                    });
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error polling for messages:', error);
                        });
                }

                // Also refresh contacts list to check for new conversations
                fetch('/api/contacts')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.contacts) {
                            contacts = data.contacts;
                            updateContactsList();
                        }
                    })
                    .catch(error => {
                        console.error('Error polling for contacts:', error);
                    });

            }, 5000); // Poll every 5 seconds
        }

        function stopPollingFallback() {
            if (messageCheckInterval) {
                console.log('Stopping message polling fallback');
                clearInterval(messageCheckInterval);
                messageCheckInterval = null;
            }
        }

        // Join confirmation event
        socket.on('join_confirmation', (data) => {
            console.log('Join confirmation received:', data);
        });

        // Receive message event
        socket.on('receive_message', (data) => {
            console.log('Received message:', data);

            // Validate message data
            if (!data || !data.sender_id || !data.receiver_id) {
                console.error('Invalid message data received:', data);
                return;
            }

            // Determine which conversation array to update
            const conversationId = data.sender_id == {{ session.get('user_id', 0) }} ? data.receiver_id : data.sender_id;

            // Initialize conversation array if it doesn't exist
            if (!conversations[conversationId]) {
                conversations[conversationId] = [];
            }

            // Check if this is a duplicate message (especially for files)
            // For our own messages, check if we already have a temporary message with the same file name
            if (data.sender_id == {{ session.get('user_id', 0) }} && data.message_type === 'file') {
                // Look for temporary messages with the same file name
                const existingTempMessage = conversations[conversationId].find(msg =>
                    msg.id.toString().startsWith('temp_') &&
                    msg.file_name === data.file_name &&
                    msg.message_type === 'file'
                );

                if (existingTempMessage) {
                    console.log('Found existing temporary file message, replacing it with permanent one');
                    // Replace the temporary message with the permanent one
                    const index = conversations[conversationId].indexOf(existingTempMessage);
                    if (index !== -1) {
                        conversations[conversationId].splice(index, 1);
                    }
                }
            }

            // Check if this is our own message coming back from the server
            const isOwnMessage = data.sender_id == {{ session.get('user_id', 0) }};

            // Create a unique message identifier that includes the status
            // This prevents treating the same message with different statuses as duplicates
            const messageKey = `${data.id}`;

            // Check if we already have this message (to prevent duplicates)
            // Use strict equality for message IDs to avoid type conversion issues
            const existingMessageIndex = conversations[conversationId].findIndex(m =>
                m.id && data.id && m.id.toString() === data.id.toString()
            );

            if (existingMessageIndex >= 0) {
                // Get the current message and its status
                const existingMessage = conversations[conversationId][existingMessageIndex];
                const currentStatus = existingMessage.status;

                // Check if this is a status update for the same message
                if (data.id === existingMessage.id && data.status !== currentStatus) {
                    console.log(`Status update for message ${data.id}: ${currentStatus} -> ${data.status}`);

                    // Only update the status if it's changing from 'sending' to something else
                    // or if it's changing to 'read'
                    if ((currentStatus === 'sending' && data.status !== 'sending') || data.status === 'read') {
                        // Just update the status field, not the entire message
                        conversations[conversationId][existingMessageIndex].status = data.status;
                        console.log(`Updated status for message ID: ${data.id} to ${data.status}`);
                    } else {
                        // Keep the current status
                        console.log(`Preserved status '${currentStatus}' for message ID: ${data.id}`);
                    }
                } else if (data.is_read !== existingMessage.is_read) {
                    // Update read status if it changed
                    conversations[conversationId][existingMessageIndex].is_read = data.is_read;
                    console.log(`Updated read status for message ID: ${data.id}`);
                } else {
                    // Skip duplicate message
                    console.log(`Skipping duplicate message with ID: ${data.id}`);
                    return; // Exit early to prevent UI updates for duplicate messages
                }
            } else if (isOwnMessage) {
                // Try to find and replace a temporary message
                const tempIndex = conversations[conversationId].findIndex(msg =>
                    msg.id && msg.id.toString().startsWith('temp_') &&
                    msg.message === data.message
                );

                if (tempIndex !== -1) {
                    console.log(`Replacing temporary message at index ${tempIndex}`);

                    // Get the current status of the temporary message
                    const currentStatus = conversations[conversationId][tempIndex].status;

                    // Replace the temporary message with the real one but preserve the status if it's 'sending'
                    const updatedMessage = {
                        id: data.id,
                        sender_id: data.sender_id,
                        sender_type: data.sender_type,
                        receiver_id: data.receiver_id,
                        receiver_type: data.receiver_type,
                        message: data.message,
                        timestamp: data.timestamp,
                        is_outgoing: true,
                        is_auto: data.is_auto || false,
                        is_read: data.is_read || false,
                        related_to_job_id: data.related_to_job_id,
                        related_to_application_id: data.related_to_application_id,
                        message_type: data.message_type || 'text',
                        // Keep the 'sending' status to preserve the clock icon
                        status: (currentStatus === 'sending') ? 'sending' : (data.status || 'sent'),
                        file_name: data.file_name,
                        file_mime_type: data.file_mime_type,
                        file_url: data.file_url
                    };

                    // Update the message in the conversation
                    conversations[conversationId][tempIndex] = updatedMessage;
                } else {
                    console.log('No temporary message found, adding as new message');
                    // Add as a new message if no temporary message was found
                    conversations[conversationId].push({
                        id: data.id,
                        sender_id: data.sender_id,
                        sender_type: data.sender_type,
                        receiver_id: data.receiver_id,
                        receiver_type: data.receiver_type,
                        message: data.message,
                        timestamp: data.timestamp,
                        is_outgoing: true,
                        is_auto: data.is_auto || false,
                        is_read: data.is_read || false,
                        related_to_job_id: data.related_to_job_id,
                        related_to_application_id: data.related_to_application_id,
                        message_type: data.message_type || 'text',
                        status: data.status || 'sent',
                        file_name: data.file_name,
                        file_mime_type: data.file_mime_type,
                        file_url: data.file_url
                    });
                }
            } else {
                console.log('Processing message from another user:', data);
                // This is a message from someone else
                conversations[conversationId].push({
                    id: data.id,
                    sender_id: data.sender_id,
                    sender_type: data.sender_type,
                    receiver_id: data.receiver_id,
                    receiver_type: data.receiver_type,
                    message: data.message,
                    timestamp: data.timestamp,
                    is_outgoing: false,
                    is_auto: data.is_auto || false,
                    is_read: data.is_read || false,
                    related_to_job_id: data.related_to_job_id,
                    related_to_application_id: data.related_to_application_id,
                    message_type: data.message_type || 'text',
                    status: data.status || 'sent',
                    file_name: data.file_name,
                    file_mime_type: data.file_mime_type,
                    file_url: data.file_url
                });

                // Play notification sound for new messages
                try {
                    const messageSound = new Audio('/static/sounds/message.mp3');
                    messageSound.volume = 0.3;
                    messageSound.play().catch(e => {
                        // Silently fail if sound can't be played
                        console.log('Could not play notification sound:', e);
                    });
                } catch (e) {
                    console.log('Error with notification sound:', e);
                }

                // Update the unread count for this contact
                const contactIndex = contacts.findIndex(c => c.id === data.sender_id);
                if (contactIndex !== -1) {
                    contacts[contactIndex].unread_count = (contacts[contactIndex].unread_count || 0) + 1;
                    contacts[contactIndex].last_message_time = data.timestamp;
                }
            }

            // Update UI if this is the current conversation
            if (currentReceiverId === conversationId) {
                // Check if this is replacing a temporary message
                const tempMessageElement = document.querySelector(`.message[data-message-id="temp_${data.id}"]`);
                if (tempMessageElement && isOwnMessage) {
                    // Replace temporary message with real message
                    updateTemporaryMessage(data);
                } else {
                    // This is a new message from another user, append it
                    if (!isOwnMessage) {
                        const newMessage = {
                            id: data.id,
                            sender_id: data.sender_id,
                            sender_type: data.sender_type,
                            receiver_id: data.receiver_id,
                            receiver_type: data.receiver_type,
                            message: data.message,
                            message_type: data.message_type,
                            timestamp: data.timestamp,
                            status: data.status,
                            is_read: data.is_read,
                            is_auto: data.is_auto,
                            file_name: data.file_name,
                            file_mime_type: data.file_mime_type,
                            file_url: data.file_url
                        };
                        appendNewMessage(newMessage, conversationId);
                    } else {
                        // For own messages that don't have temp messages, use displayMessages
                        displayMessages(currentReceiverId);
                    }
                }

                // If we're viewing the conversation with the sender, mark messages as read
                if (!isOwnMessage) {
                    socket.emit('mark_messages_read', {
                        contact_id: data.sender_id
                    });
                }
            }

            // Update contact list
            updateContactsList();
        });

        // Handle messages marked as read event
        socket.on('messages_marked_read', (data) => {
            console.log('Messages marked as read:', data);

            // Update the unread count for this contact
            const contactIndex = contacts.findIndex(c => c.id === data.contact_id);
            if (contactIndex !== -1) {
                contacts[contactIndex].unread_count = data.unread_count;

                // Update the UI
                updateContactsList();
            }

            // If this is the current conversation, update message status
            if (currentReceiverId === data.contact_id) {
                // Update is_read status for messages in this conversation
                if (conversations[data.contact_id]) {
                    conversations[data.contact_id].forEach(msg => {
                        if (msg.sender_id == {{ session.get('user_id', 0) }} && !msg.is_read) {
                            msg.is_read = true;
                        }
                    });

                    // Refresh the display
                    displayMessages(data.contact_id);
                }
            }
        });

        // Handle fallback for marking messages as read when Socket.IO fails
        socket.on('fallback_mark_read', (data) => {
            console.log('Using fallback to mark messages as read:', data);

            // Use the REST API as a fallback
            fetch(`/api/mark_messages_read/${data.contact_id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(result => {
                console.log('Fallback mark as read result:', result);

                if (result.success) {
                    // Update the unread count for this contact
                    const contactIndex = contacts.findIndex(c => c.id === data.contact_id);
                    if (contactIndex !== -1) {
                        contacts[contactIndex].unread_count = result.unread_count;
                        // Update the UI
                        updateContactsList();
                    }

                    // If this is the current conversation, update message status
                    if (currentReceiverId === data.contact_id) {
                        // Update is_read status for messages in this conversation
                        if (conversations[data.contact_id]) {
                            conversations[data.contact_id].forEach(msg => {
                                if (msg.sender_id == {{ session.get('user_id', 0) }} && !msg.is_read) {
                                    msg.is_read = true;
                                }
                            });

                            // Refresh the display
                            displayMessages(data.contact_id);
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error with fallback mark as read:', error);
            });
        });

        // Handle Socket.IO errors
        socket.on('error', (data) => {
            console.error('Socket.IO error:', data);

            // If the error is related to marking messages as read, try the fallback
            if (data.msg && data.msg.includes('marking messages as read')) {
                console.log('Error marking messages as read, will try fallback on next view');
            }
        });

        // Handle message status updates
        socket.on('message_status_update', (data) => {
            console.log('Message status update received:', data);

            if (!data || !data.message_id || !data.status) {
                console.error('Invalid message status update data:', data);
                return;
            }

            // Update the message status in all conversations
            for (const conversationId in conversations) {
                if (conversations.hasOwnProperty(conversationId)) {
                    const messageIndex = conversations[conversationId].findIndex(msg =>
                        msg.id && msg.id.toString() === data.message_id.toString()
                    );

                    if (messageIndex >= 0) {
                        // Update the status
                        const oldStatus = conversations[conversationId][messageIndex].status;
                        conversations[conversationId][messageIndex].status = data.status;
                        console.log(`Updated message ${data.message_id} status: ${oldStatus} -> ${data.status}`);

                        // Update the UI if this is the current conversation
                        if (currentReceiverId === parseInt(conversationId)) {
                            displayMessages(currentReceiverId);
                        }
                    }
                }
            }
        });

        // Handle reaction added event
        socket.on('reaction_added', (data) => {
            // Update the UI for the message with the complete reaction data
            if (data.reaction && data.message_id) {
                console.log('Real-time reaction added:', data);

                // Immediately update the UI with the new reaction
                const reactionsContainer = document.getElementById(`reactions-${data.message_id}`);
                if (reactionsContainer) {
                    // Get existing reactions or initialize empty array
                    let existingReactions = reactionsCache.data[data.message_id] || [];

                    // Check if this reaction already exists
                    const existingIndex = existingReactions.findIndex(
                        r => r.user_id == data.reaction.user_id && r.emoji == data.reaction.emoji
                    );

                    if (existingIndex >= 0) {
                        // Update existing reaction
                        existingReactions[existingIndex] = data.reaction;
                    } else {
                        // Add new reaction
                        existingReactions.push(data.reaction);
                    }

                    // Update cache
                    reactionsCache.data[data.message_id] = existingReactions;
                    reactionsCache.timestamp[data.message_id] = Date.now();

                    // Instead of refreshing all reactions, just update the specific emoji
                    // Use updateReactionUI to only update the specific emoji
                    updateReactionUI(data.message_id, data.reaction.emoji, true);
                }

                // Find the message in all conversations
                let messageFound = false;
                let conversationId = null;

                // Search through all conversations to find the message
                Object.keys(conversations).forEach(contactId => {
                    const msgIndex = conversations[contactId].findIndex(m => m.id == data.message_id);
                    if (msgIndex >= 0) {
                        messageFound = true;
                        conversationId = contactId;

                        // Add the reaction to the message
                        if (!conversations[contactId][msgIndex].reactions) {
                            conversations[contactId][msgIndex].reactions = [];
                        }

                        // Check if this reaction already exists
                        const existingIndex = conversations[contactId][msgIndex].reactions.findIndex(
                            r => r.user_id == data.reaction.user_id && r.emoji == data.reaction.emoji
                        );

                        if (existingIndex >= 0) {
                            // Update existing reaction
                            conversations[contactId][msgIndex].reactions[existingIndex] = data.reaction;
                        } else {
                            // Add new reaction
                            conversations[contactId][msgIndex].reactions.push(data.reaction);
                        }
                    }
                });

                // Update the contacts list to reflect the new reaction
                updateContactsList();

                // Play a subtle sound for reactions if not from current user
                if (data.reaction.user_id != {{ session.get('user_id', 0) }}) {
                    // Optional: Add a subtle notification sound here
                    const reactionSound = new Audio('/static/sounds/reaction.mp3');
                    reactionSound.volume = 0.2;
                    reactionSound.play().catch(e => {
                        // Silently fail if sound can't be played (e.g., no user interaction yet)
                    });

                    // Show a brief visual feedback for new reactions
                    const reactionMessageElement = document.querySelector(`.message[data-message-id="${data.message_id}"]`);
                    if (reactionMessageElement) {
                        reactionMessageElement.classList.add('reaction-highlight');
                        setTimeout(() => {
                            reactionMessageElement.classList.remove('reaction-highlight');
                        }, 1000);
                    }

                    // If this is a new reaction from another user, also update the UI directly
                    // This ensures the reaction is visible immediately without waiting for a fetch
                    const emoji = data.reaction.emoji;
                    const messageId = data.message_id;

                    // Use our helper function to update the UI
                    const reactionItem = reactionsContainer.querySelector(`.reaction-item[data-emoji="${emoji}"]`);
                    if (!reactionItem) {
                        // Create new reaction item if it doesn't exist
                        const newReactionItem = document.createElement('div');
                        newReactionItem.className = 'reaction-item';
                        newReactionItem.setAttribute('data-emoji', emoji);
                        newReactionItem.setAttribute('data-timestamp', data.reaction.created_at);
                        newReactionItem.style.display = 'inline-flex !important';
                        newReactionItem.style.visibility = 'visible !important';
                        newReactionItem.style.opacity = '1 !important';

                        // Format the timestamp
                        const reactionTime = formatReactionTime(data.reaction.created_at);

                        newReactionItem.innerHTML = `
                            <span class="reaction-emoji">${emoji}</span>
                            <span class="reaction-count">1</span>
                            <span class="reaction-time" title="${new Date(data.reaction.created_at).toLocaleString()}">${reactionTime}</span>
                        `;

                        // Add click handler
                        newReactionItem.onclick = function() {
                            addReaction(messageId, emoji);
                        };

                        reactionsContainer.appendChild(newReactionItem);
                    } else {
                        // Update existing reaction count
                        const countElement = reactionItem.querySelector('.reaction-count');
                        let count = parseInt(countElement.textContent);
                        count++;
                        countElement.textContent = count;
                    }

                    // Make sure the container is visible
                    reactionsContainer.style.display = 'flex';
                    reactionsContainer.style.visibility = 'visible';
                    reactionsContainer.style.opacity = '1';
                }
            }
        });

        // Handle reaction removed event
        socket.on('reaction_removed', (data) => {
            // When a reaction is removed, immediately update the UI
            if (data.message_id) {
                console.log('Real-time reaction removed:', data);

                // Immediately update the UI by removing the reaction
                const reactionsContainer = document.getElementById(`reactions-${data.message_id}`);
                if (reactionsContainer) {
                    // Get existing reactions from cache
                    let existingReactions = reactionsCache.data[data.message_id] || [];

                    // Filter out the removed reaction
                    existingReactions = existingReactions.filter(
                        r => !(r.user_id == data.user_id && r.emoji == data.emoji)
                    );

                    // Update cache
                    reactionsCache.data[data.message_id] = existingReactions;
                    reactionsCache.timestamp[data.message_id] = Date.now();

                    // Instead of refreshing all reactions, just update the specific emoji
                    // Use updateReactionUI to only update the specific emoji
                    updateReactionUI(data.message_id, data.emoji, false);
                }

                // Find the message in all conversations
                let messageFound = false;
                let conversationId = null;

                // Search through all conversations to find the message
                Object.keys(conversations).forEach(contactId => {
                    const msgIndex = conversations[contactId].findIndex(m => m.id == data.message_id);
                    if (msgIndex >= 0) {
                        messageFound = true;
                        conversationId = contactId;

                        // Remove the reaction from the message
                        if (conversations[contactId][msgIndex].reactions) {
                            // Filter out the removed reaction
                            conversations[contactId][msgIndex].reactions = conversations[contactId][msgIndex].reactions.filter(
                                r => !(r.user_id == data.user_id && r.emoji == data.emoji)
                            );
                        }
                    }
                });

                // Update the contacts list to reflect the removed reaction
                updateContactsList();

                // If this is a reaction removed by another user, play a subtle sound
                if (data.user_id != {{ session.get('user_id', 0) }}) {
                    // Optional: Add a subtle notification sound here
                    const reactionSound = new Audio('/static/sounds/reaction-remove.mp3');
                    reactionSound.volume = 0.1;
                    reactionSound.play().catch(e => {
                        // Silently fail if sound can't be played
                    });
                }
            }
        });

        // Handle online users list
        socket.on('online_users_list', (data) => {
            console.log('Online users list received:', data);
            if (data.online_users) {
                onlineUsers = new Set(data.online_users.map(user => user.user_id.toString()));
                updateContactsOnlineStatus();
            }
        });

        // Handle user online status
        socket.on('user_online', (data) => {
            console.log('User came online:', data);
            if (data.user_id) {
                onlineUsers.add(data.user_id.toString());
                updateContactOnlineStatus(data.user_id, 'online');
            }
        });

        // Handle user offline status
        socket.on('user_offline', (data) => {
            console.log('User went offline:', data);
            if (data.user_id) {
                onlineUsers.delete(data.user_id.toString());
                updateContactOnlineStatus(data.user_id, 'offline');
            }
        });

        // Function to load contacts
        function loadContacts() {
            // Show loading indicator
            document.getElementById('contacts-container').innerHTML = `
                <div class="p-4 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading conversations...</p>
                </div>
            `;

            // Fetch contacts from API
            fetch('/api/contacts')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.contacts && data.contacts.length > 0) {
                        // Store contacts
                        contacts = data.contacts;

                        // Update the UI first
                        updateContactsList();

                        // Load messages for the first contact only
                        if (contacts.length > 0 && !currentReceiverId) {
                            // First explicitly load messages for the first contact
                            console.log("Initial load for first contact:", contacts[0].id);
                            loadMessagesForContact(contacts[0].id, { showLoading: true }).then(() => {
                                // Then select the contact to display the messages
                                selectContact(contacts[0].id, getContactName(contacts[0]), contacts[0].profile_photo || '');
                            });
                        }

                        // Load messages for other contacts in the background
                        setTimeout(() => {
                            contacts.forEach(contact => {
                                if (contact.id !== currentReceiverId) {
                                    console.log("Background loading for contact:", contact.id);
                                    loadMessagesForContact(contact.id, { showLoading: false });
                                }
                            });
                        }, 1000);
                    } else {
                        // No contacts, show empty state
                        document.getElementById('contacts-container').innerHTML = `
                            <div class="p-4 text-center text-muted">
                                <i class="fas fa-inbox mb-3 d-block" style="font-size: 2rem;"></i>
                                <p>No conversations yet</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading contacts:', error);
                    // Fallback to empty state
                    document.getElementById('contacts-container').innerHTML = `
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-exclamation-circle mb-3 d-block" style="font-size: 2rem;"></i>
                            <p>Error loading conversations</p>
                        </div>
                    `;
                });
        }

        // Function to load messages for a specific contact
        function loadMessagesForContact(contactId, options = {}) {
            const { page = 1, limit = 10, beforeId = null, showLoading = true } = options;

            // Initialize conversation array if it doesn't exist
            if (!conversations[contactId]) {
                conversations[contactId] = [];
                conversations[contactId].metadata = {
                    hasMore: true,
                    isLoading: false,
                    page: 1,
                    totalCount: 0,
                    lastFetchTime: 0,
                    messageIds: new Set() // Track message IDs for faster existence check
                };
            }

            // Don't load if we're already loading
            if (conversations[contactId].metadata && conversations[contactId].metadata.isLoading) {
                return Promise.resolve(false);
            }

            // Don't load if there are no more messages and we're trying to load older messages
            if (conversations[contactId].metadata && !conversations[contactId].metadata.hasMore && beforeId) {
                return Promise.resolve(false);
            }

            // Implement cache expiration - only fetch new messages if it's been more than 30 seconds
            // This prevents excessive API calls when switching between conversations
            const now = Date.now();
            const lastFetchTime = conversations[contactId].metadata.lastFetchTime || 0;
            const cacheExpiration = 30 * 1000; // 30 seconds

            if (!beforeId && now - lastFetchTime < cacheExpiration && conversations[contactId].length > 0) {
                console.log(`Using cached messages for contact ${contactId}, cache age: ${(now - lastFetchTime)/1000}s`);

                // If this is the current conversation, update the display from cache
                if (currentReceiverId === contactId) {
                    displayMessages(contactId);
                }

                return Promise.resolve(true);
            }

            // Set loading state
            if (conversations[contactId].metadata) {
                conversations[contactId].metadata.isLoading = true;
            }

            // Show loading indicator if requested
            if (showLoading && currentReceiverId === contactId) {
                if (beforeId) {
                    // Show loading indicator at the top for older messages
                    const messagesContainer = document.getElementById('chat-messages');
                    const loadingIndicator = document.createElement('div');
                    loadingIndicator.id = 'loading-indicator';
                    loadingIndicator.className = 'text-center p-2';
                    loadingIndicator.innerHTML = `
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">Loading older messages...</span>
                    `;
                    messagesContainer.prepend(loadingIndicator);

                    // Save scroll position
                    const scrollHeight = messagesContainer.scrollHeight;
                    conversations[contactId].scrollPosition = scrollHeight;
                } else if (conversations[contactId].length === 0) {
                    // Only show full loading indicator for initial load
                    const messagesContainer = document.getElementById('chat-messages');
                    messagesContainer.innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading messages...</p>
                        </div>
                    `;
                }
            }

            // Build URL with query parameters
            let url = `/api/messages/${contactId}?limit=${limit}&page=${page}`;
            if (beforeId) {
                url += `&before_id=${beforeId}`;
            }

            console.log(`Fetching messages from: ${url}`);
            return fetch(url)
                .then(response => response.json())
                .then(data => {
                    // Remove loading indicator
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    // Update cache timestamp
                    if (conversations[contactId].metadata) {
                        conversations[contactId].metadata.lastFetchTime = Date.now();
                    }

                    if (data.success && data.messages) {
                        console.log(`Received ${data.messages.length} messages, total: ${data.total_count}, has_more: ${data.has_more}`);

                        // Update metadata
                        if (conversations[contactId].metadata) {
                            conversations[contactId].metadata.hasMore = data.has_more;
                            conversations[contactId].metadata.page = data.page;
                            conversations[contactId].metadata.totalCount = data.total_count;
                            conversations[contactId].metadata.isLoading = false;
                        }

                        // Process messages - use Set for faster existence check
                        const newMessages = [];
                        const messageIds = conversations[contactId].metadata.messageIds || new Set();

                        // First, convert all existing message IDs to strings for consistent comparison
                        const existingMessageIds = new Set();
                        conversations[contactId].forEach(msg => {
                            if (msg.id) {
                                existingMessageIds.add(msg.id.toString());
                            }
                        });

                        data.messages.forEach(msg => {
                            // Convert message ID to string for consistent comparison
                            const msgIdStr = msg.id ? msg.id.toString() : null;

                            // Check if message already exists using Set for O(1) lookup
                            // Check both the metadata Set and the actual messages array
                            if (msgIdStr && !messageIds.has(msgIdStr) && !existingMessageIds.has(msgIdStr)) {
                                messageIds.add(msgIdStr);

                                const processedMsg = {
                                    id: msg.id,
                                    sender_id: msg.sender_id,
                                    sender_type: msg.sender_type,
                                    receiver_id: msg.receiver_id,
                                    receiver_type: msg.receiver_type,
                                    message: msg.message,
                                    timestamp: msg.timestamp || msg.created_at,
                                    is_outgoing: msg.sender_id == {{ session.get('user_id', 0) }},
                                    is_auto: msg.is_auto,
                                    is_read: msg.is_read,
                                    related_to_job_id: msg.related_to_job_id,
                                    related_to_application_id: msg.related_to_application_id,
                                    message_type: msg.message_type,
                                    status: msg.status,
                                    is_deleted: msg.is_deleted,
                                    deleted_by_sender: msg.deleted_by_sender,
                                    deleted_by_receiver: msg.deleted_by_receiver,
                                    reply_to_id: msg.reply_to_id,
                                    replied_message_text: msg.replied_message_text,
                                    file_name: msg.file_name,
                                    file_mime_type: msg.file_mime_type,
                                    file_url: msg.file_url,
                                    // Store reactions from the server if available
                                    reactions: msg.reactions || []
                                };
                                newMessages.push(processedMsg);
                            }
                        });

                        // Save the message IDs Set back to metadata
                        conversations[contactId].metadata.messageIds = messageIds;

                        // Add messages to conversation
                        if (beforeId) {
                            // Add older messages at the beginning
                            conversations[contactId].unshift(...newMessages);
                        } else {
                            // Add newer messages at the end
                            conversations[contactId].push(...newMessages);
                        }

                        // If this is the current conversation, update the display
                        if (currentReceiverId === contactId) {
                            if (beforeId) {
                                // Preserve scroll position when loading older messages
                                displayMessages(contactId, { preserveScroll: true, scrollPosition: conversations[contactId].scrollPosition });
                            } else {
                                displayMessages(contactId);
                            }
                        }

                        // Update contact list to show latest message
                        updateContactsList();

                        return true;
                    }

                    // Reset loading state on error
                    if (conversations[contactId].metadata) {
                        conversations[contactId].metadata.isLoading = false;
                    }
                    return false;
                })
                .catch(error => {
                    console.error(`Error loading messages for contact ${contactId}:`, error);

                    // Reset loading state on error
                    if (conversations[contactId].metadata) {
                        conversations[contactId].metadata.isLoading = false;
                    }

                    // Remove loading indicator
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    return false;
                });
        }

        // Helper function to get contact name
        function getContactName(contact) {
            let contactName = 'Unknown';
            if (contact.first_name && contact.last_name) {
                contactName = `${contact.first_name} ${contact.last_name}`;
            } else if (contact.first_name) {
                contactName = contact.first_name;
            } else if (contact.last_name) {
                contactName = contact.last_name;
            } else if (contact.business_name) {
                contactName = contact.business_name;
            }
            return contactName;
        }

        // Function to update contacts list
        function updateContactsList() {
            const contactsContainer = document.getElementById('contacts-container');
            contactsContainer.innerHTML = '';

            if (!contacts || contacts.length === 0) {
                contactsContainer.innerHTML = `
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-inbox mb-3 d-block" style="font-size: 2rem;"></i>
                        <p>No conversations yet</p>
                    </div>
                `;
                return;
            }

            // Sort contacts by last message time
            contacts.sort((a, b) => {
                const aTime = new Date(a.last_message_time || '1970-01-01');
                const bTime = new Date(b.last_message_time || '1970-01-01');
                return bTime - aTime;
            });

            // Create a document fragment for better performance
            const fragment = document.createDocumentFragment();

            contacts.forEach(contact => {
                // Get the last message
                let lastMessage = '';
                let lastMessageType = 'text';
                let previewText = '';

                if (conversations[contact.id] && conversations[contact.id].length > 0) {
                    // Sort messages by timestamp to ensure we get the actual last message
                    const sortedMessages = [...conversations[contact.id]].sort((a, b) => {
                        const aTime = new Date(a.timestamp);
                        const bTime = new Date(b.timestamp);
                        return bTime - aTime; // Descending order (newest first)
                    });

                    // Get the most recent message
                    const lastMsg = sortedMessages[0];
                    lastMessage = lastMsg.message || '';
                    lastMessageType = lastMsg.message_type || 'text';

                    // Format the preview text based on message type
                    if (lastMessageType === 'file') {
                        // Show simple "Sent a photo" or "Sent a file" text
                        if (lastMsg.file_mime_type && lastMsg.file_mime_type.startsWith('image/')) {
                            previewText = "Sent a photo";
                        } else {
                            previewText = "Sent a file";
                        }
                    } else {
                        previewText = lastMessage;
                    }

                    // Add reaction emoji to preview if there are any reactions
                    if (lastMsg.reactions && lastMsg.reactions.length > 0) {
                        // Group reactions by emoji
                        const reactionCounts = {};
                        lastMsg.reactions.forEach(reaction => {
                            if (!reactionCounts[reaction.emoji]) {
                                reactionCounts[reaction.emoji] = 0;
                            }
                            reactionCounts[reaction.emoji]++;
                        });

                        // Add reaction emojis to preview
                        const reactionEmojis = Object.keys(reactionCounts).map(emoji =>
                            `${emoji}${reactionCounts[emoji] > 1 ? reactionCounts[emoji] : ''}`
                        ).join(' ');

                        if (reactionEmojis) {
                            previewText += ` [${reactionEmojis}]`;
                        }
                    }
                }

                // Get contact name
                const contactName = getContactName(contact);

                // Get avatar
                const avatar = contact.profile_photo || '';

                // Get unread count
                const unreadCount = contact.unread_count || 0;

                // Determine online status
                const isOnline = onlineUsers.has(contact.id.toString());
                const onlineStatusClass = isOnline ? 'online' : 'offline';

                // Create contact element
                const contactElement = document.createElement('div');
                contactElement.className = `contact-item ${currentReceiverId === contact.id ? 'active' : ''}`;
                contactElement.setAttribute('data-contact-id', contact.id);
                contactElement.onclick = function() {
                    // If already active, just highlight it
                    if (currentReceiverId === contact.id) {
                        // Just update the active class without refreshing messages
                        document.querySelectorAll('.contact-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        this.classList.add('active');

                        // Still mark messages as read even if we're not refreshing
                        if (contact.unread_count > 0) {
                            socket.emit('mark_messages_read', {
                                contact_id: contact.id
                            });

                            // Update the unread count in the UI
                            contact.unread_count = 0;
                            updateContactsList();
                        }
                    } else {
                        // Otherwise, select the contact normally
                        selectContact(contact.id, contactName, avatar);
                    }
                };

                contactElement.innerHTML = `
                    <img src="${avatar || `/api/profile-photo/${contact.user_type}/${contact.id}`}"
                         alt="${contactName}" class="contact-avatar ${onlineStatusClass}">
                    <div class="contact-info">
                        <div class="contact-name">
                            ${contactName}
                            <span class="contact-status-indicator ${onlineStatusClass}"></span>
                        </div>
                        <div class="contact-preview">${previewText}</div>
                    </div>
                    <div class="contact-meta">
                        <div class="contact-time">${formatTime(contact.last_message_time || '')}</div>
                        ${unreadCount > 0 ? `<div class="unread-badge">${unreadCount}</div>` : ''}
                    </div>
                `;

                fragment.appendChild(contactElement);
            });

            // Append all contacts at once
            contactsContainer.appendChild(fragment);
        }

        // Function to update all contacts online status
        function updateContactsOnlineStatus() {
            if (!contacts || contacts.length === 0) return;

            contacts.forEach(contact => {
                updateContactOnlineStatus(contact.id, onlineUsers.has(contact.id.toString()) ? 'online' : 'offline');
            });
        }

        // Function to update specific contact online status
        function updateContactOnlineStatus(contactId, status) {
            // Update contact avatar status indicator
            const contactElement = document.querySelector(`[data-contact-id="${contactId}"]`);
            if (contactElement) {
                const avatar = contactElement.querySelector('.contact-avatar');
                const statusIndicator = contactElement.querySelector('.contact-status-indicator');

                if (avatar) {
                    // Remove all status classes
                    avatar.classList.remove('online', 'offline', 'away');
                    // Add new status class
                    avatar.classList.add(status);
                }

                if (statusIndicator) {
                    // Remove all status classes
                    statusIndicator.classList.remove('online', 'offline', 'away');
                    // Add new status class
                    statusIndicator.classList.add(status);
                }
            }

            // Update chat header status if this is the current contact
            if (currentReceiverId && currentReceiverId.toString() === contactId.toString()) {
                updateChatHeaderStatus(status);
            }
        }

        // Function to update chat header status
        function updateChatHeaderStatus(status) {
            const contactStatus = document.getElementById('contact-status');
            if (contactStatus) {
                let statusText = 'Offline';
                switch(status) {
                    case 'online':
                        statusText = 'Online';
                        break;
                    case 'away':
                        statusText = 'Away';
                        break;
                    case 'offline':
                    default:
                        statusText = 'Offline';
                        break;
                }
                contactStatus.textContent = statusText;
                contactStatus.className = `small text-muted status-${status}`;
            }
        }

        // Function to select a contact
        function selectContact(contactId, contactName, contactAvatar) {
            // Check if we're already on this contact to avoid unnecessary refreshing
            if (currentReceiverId === contactId) {
                console.log("Already on this contact, not refreshing messages");
                return; // Exit early if we're already on this contact
            }

            // Set the current receiver ID
            currentReceiverId = contactId;

            // Find the selected contact
            const selectedContact = contacts.find(c => c.id === contactId);

            // Determine online status
            const isOnline = onlineUsers.has(contactId.toString());
            const statusText = isOnline ? 'Online' : 'Offline';
            const statusClass = isOnline ? 'online' : 'offline';

            // Update chat header
            document.getElementById('chat-header').innerHTML = `
                <div class="chat-header-left">
                    <button id="toggle-contacts-btn" class="btn btn-sm d-lg-none me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <img src="${contactAvatar || `/api/profile-photo/${selectedContact.user_type}/${contactId}`}"
                         alt="${contactName}" class="contact-avatar ${statusClass}">
                    <div class="contact-info">
                        <div id="contact-name">${contactName}</div>
                        <div id="contact-status" class="small text-muted status-${statusClass}">${statusText}</div>
                    </div>
                </div>
                <div class="chat-header-right">
                    <button id="chat-info-btn" class="chat-info-btn" onclick="toggleChatInfo()">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            `;

            // Show chat content and hide empty state
            document.getElementById('empty-state').style.display = 'none';
            document.getElementById('chat-content').style.display = 'flex';

            // Enable input
            document.getElementById('message-input').disabled = false;
            document.getElementById('send-button').disabled = false;
            document.getElementById('receiver-id').value = contactId;

            // Load messages for this contact if not already loaded
            console.log("Loading messages for contact:", contactId);
            loadMessagesForContact(contactId).then(() => {
                // Display messages after loading
                displayMessages(contactId);

                // Mark messages as read using Socket.IO for real-time updates
                socket.emit('mark_messages_read', {
                    contact_id: contactId
                });
            });

            // Update active state in contacts list
            updateContactsList();

            // Update chat info sidebar with proper contact data
            const chatInfoData = {
                name: contactName,
                profile_photo: contactAvatar || `/api/profile-photo/${selectedContact.user_type}/${contactId}`
            };
            console.log('Updating chat info with data:', chatInfoData);
            updateChatInfo(chatInfoData);

            // Re-add event listener for toggle contacts button
            const toggleBtn = document.getElementById('toggle-contacts-btn');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    const contactsPanel = document.querySelector('.contacts-panel');
                    contactsPanel.classList.toggle('show');
                });
            }
        }

        // Function to display messages
        function displayMessages(contactId, options = {}) {
            const { preserveScroll = false, scrollPosition = 0, preserveReactions = true } = options;

            if (!conversations[contactId] || conversations[contactId].length === 0) {
                return;
            }

            const messagesContainer = document.getElementById('chat-messages');
            const scrolledToBottom = messagesContainer.scrollHeight - messagesContainer.scrollTop <= messagesContainer.clientHeight + 100;

            // Save scroll position if we're preserving it
            const oldScrollHeight = preserveScroll ? messagesContainer.scrollHeight : 0;

            // Store ALL existing messages and their reactions
            const existingMessages = new Set();
            const existingReactions = {};

            // Always preserve reactions by default
            document.querySelectorAll('.message').forEach(messageEl => {
                const messageId = messageEl.getAttribute('data-message-id');
                if (messageId) {
                    existingMessages.add(messageId.toString());

                    // Store reactions for this message if they exist
                    const reactionsContainer = messageEl.querySelector('.message-reactions');
                    if (reactionsContainer && reactionsContainer.innerHTML.trim() !== '') {
                        existingReactions[messageId] = reactionsContainer.innerHTML;
                    }
                }
            });

            // Only clear container if we're showing a different conversation
            const currentConversationId = messagesContainer.getAttribute('data-conversation-id');
            if (currentConversationId !== contactId.toString()) {
                messagesContainer.innerHTML = '';
                messagesContainer.setAttribute('data-conversation-id', contactId.toString());
            }

            // Add load more button if there are more messages to load
            if (conversations[contactId] &&
                conversations[contactId].metadata &&
                conversations[contactId].metadata.hasMore) {

                // Check if load more button already exists
                if (!document.getElementById('load-more-messages')) {
                    const loadMoreButton = document.createElement('div');
                    loadMoreButton.id = 'load-more-messages';
                    loadMoreButton.className = 'text-center p-2 mb-2';
                    loadMoreButton.innerHTML = `
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-up me-1"></i> Load older messages
                        </button>
                    `;
                    loadMoreButton.onclick = function() {
                        // Get the first message ID to load messages before it
                        if (conversations[contactId] && conversations[contactId].length > 0) {
                            const firstMessageId = conversations[contactId][0].id;
                            loadMessagesForContact(contactId, { beforeId: firstMessageId });
                        }
                    };
                    messagesContainer.prepend(loadMoreButton);
                }
            } else {
                // Remove load more button if it exists and there are no more messages
                const loadMoreButton = document.getElementById('load-more-messages');
                if (loadMoreButton) {
                    loadMoreButton.remove();
                }

                // Add "start of conversation" message if we've loaded all messages
                if (conversations[contactId] && conversations[contactId].length > 0) {
                    const startOfConversation = document.createElement('div');
                    startOfConversation.className = 'system-message';
                    startOfConversation.innerHTML = '<span>Start of conversation</span>';

                    // Insert after load more button or at the beginning
                    const loadMoreButton = document.getElementById('load-more-messages');
                    if (loadMoreButton) {
                        loadMoreButton.after(startOfConversation);
                    } else {
                        messagesContainer.prepend(startOfConversation);
                    }
                }
            }

            // If no messages, show empty state
            if (!conversations[contactId] || conversations[contactId].length === 0) {
                if (!document.querySelector('.system-message')) {
                    messagesContainer.innerHTML = `
                        <div class="system-message">
                            <span>Start of conversation</span>
                        </div>
                    `;
                }
                return;
            }

            // Create a document fragment for better performance
            const fragment = document.createDocumentFragment();

            // Sort messages by timestamp
            const messages = [...conversations[contactId]].sort((a, b) => {
                const aTime = new Date(a.timestamp);
                const bTime = new Date(b.timestamp);
                return aTime - bTime;
            });

            let lastDate = '';

            // Find existing date separators to avoid duplicates
            const existingDates = new Set();
            document.querySelectorAll('.system-message').forEach(el => {
                if (el.textContent && !el.textContent.includes('Start of conversation')) {
                    existingDates.add(el.textContent.trim());
                }
            });

            // Only render messages that don't already exist in the DOM
            // This prevents duplicate messages and preserves existing reactions

            // Clear existing messages first to prevent duplicates
            messagesContainer.innerHTML = '';

            // Track which messages need reactions
            const messagesToFetchReactions = [];

            messages.forEach(msg => {
                // Since we're clearing the container, we don't need to skip messages
                // that were previously rendered

                const messageDate = new Date(msg.timestamp).toLocaleDateString();

                // Add date separator if needed and not already present
                if (messageDate !== lastDate && !existingDates.has(messageDate)) {
                    const dateSeparator = document.createElement('div');
                    dateSeparator.className = 'system-message';
                    dateSeparator.innerHTML = `<span>${messageDate}</span>`;
                    fragment.appendChild(dateSeparator);
                    existingDates.add(messageDate);
                    lastDate = messageDate;
                }

                const isOutgoing = msg.sender_id == {{ session.get('user_id', 0) }};
                const messageTime = formatMessageTime(msg.timestamp);

                // Skip deleted messages unless they're from the current user
                if (msg.is_deleted && ((isOutgoing && msg.deleted_by_sender) || (!isOutgoing && msg.deleted_by_receiver))) {
                    return;
                }

                // Handle different message types
                let messageContent = '';

                if (msg.is_deleted) {
                    messageContent = '<em class="text-muted">This message was deleted</em>';
                } else if (msg.message_type === 'file') {
                    // For temporary messages or messages with file_url
                    // Generate file URL with cache-busting parameter to prevent caching issues
                    const timestamp = new Date().getTime();
                    const fileUrl = msg.file_url || `/api/files/${msg.id}?t=${timestamp}`;

                    console.log(`File message: id=${msg.id}, mime=${msg.file_mime_type}, url=${fileUrl}`);

                    // Only show file preview for non-temporary messages
                    if (!msg.id.toString().startsWith('temp_')) {
                        if (msg.file_mime_type && msg.file_mime_type.startsWith('image/')) {
                            messageContent = `
                                <div class="message-image">
                                    <img src="${fileUrl}" alt="${msg.file_name}" class="img-fluid rounded"
                                         onerror="this.onerror=null; this.src='/static/img/image-placeholder.png'; console.error('Failed to load image: ${fileUrl}');">
                                    <div class="mt-1 small">${msg.file_name}</div>
                                </div>
                            `;
                        } else {
                            // Determine file icon based on mime type
                            let fileIcon = 'fa-file';
                            if (msg.file_mime_type) {
                                if (msg.file_mime_type.includes('pdf')) {
                                    fileIcon = 'fa-file-pdf';
                                } else if (msg.file_mime_type.includes('word') || msg.file_mime_type.includes('document')) {
                                    fileIcon = 'fa-file-word';
                                } else if (msg.file_mime_type.includes('excel') || msg.file_mime_type.includes('sheet')) {
                                    fileIcon = 'fa-file-excel';
                                } else if (msg.file_mime_type.includes('zip') || msg.file_mime_type.includes('archive')) {
                                    fileIcon = 'fa-file-archive';
                                } else if (msg.file_mime_type.includes('audio')) {
                                    fileIcon = 'fa-file-audio';
                                } else if (msg.file_mime_type.includes('video')) {
                                    fileIcon = 'fa-file-video';
                                }
                            }

                            messageContent = `
                                <div class="message-file">
                                    <a href="${fileUrl}" target="_blank" class="d-flex align-items-center text-decoration-none">
                                        <i class="fas ${fileIcon} me-2"></i>
                                        <span>${msg.file_name}</span>
                                    </a>
                                </div>
                            `;
                        }
                    } else {
                        // For temporary messages, show preview for images or icon for other files
                        if (msg.file_mime_type && msg.file_mime_type.startsWith('image/')) {
                            // For image files, use the stored file URL if available
                            if (msg.file_url) {
                                // Use the data URL that was stored in the message object
                                messageContent = `
                                    <div class="message-image">
                                        <img src="${msg.file_url}" alt="${msg.file_name}" class="img-fluid rounded"
                                             onerror="this.onerror=null; this.src='/static/img/image-placeholder.png'; console.error('Failed to load image: ${msg.file_url}');">
                                        <div class="mt-1 small">${msg.file_name} <span class="ms-2">(Sending...)</span></div>
                                    </div>
                                `;
                            } else {
                                // Fallback if no file URL is available
                                messageContent = `
                                    <div class="message-file">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-image me-2"></i>
                                            <span>${msg.file_name}</span>
                                            <span class="ms-2 text-muted small">(Sending...)</span>
                                        </div>
                                    </div>
                                `;
                            }
                        } else {
                            // For non-image files, show icon
                            let fileIcon = 'fa-file';
                            if (msg.file_mime_type) {
                                if (msg.file_mime_type.includes('pdf')) {
                                    fileIcon = 'fa-file-pdf';
                                } else if (msg.file_mime_type.includes('word') || msg.file_mime_type.includes('document')) {
                                    fileIcon = 'fa-file-word';
                                } else if (msg.file_mime_type.includes('excel') || msg.file_mime_type.includes('sheet')) {
                                    fileIcon = 'fa-file-excel';
                                } else if (msg.file_mime_type.includes('zip') || msg.file_mime_type.includes('archive')) {
                                    fileIcon = 'fa-file-archive';
                                } else if (msg.file_mime_type.includes('audio')) {
                                    fileIcon = 'fa-file-audio';
                                } else if (msg.file_mime_type.includes('video')) {
                                    fileIcon = 'fa-file-video';
                                }
                            }

                            messageContent = `
                                <div class="message-file">
                                    <div class="d-flex align-items-center">
                                        <i class="fas ${fileIcon} me-2"></i>
                                        <span>${msg.file_name}</span>
                                        <span class="ms-2 text-muted small">(Sending...)</span>
                                    </div>
                                </div>
                            `;
                        }
                    }
                } else {
                    // Safely handle message content
                    messageContent = msg.message || '';

                    // Convert URLs to clickable links
                    messageContent = messageContent.replace(
                        /(https?:\/\/[^\s]+)/g,
                        '<a href="$1" target="_blank" class="text-decoration-underline">$1</a>'
                    );
                }

                // Determine if this is an auto message
                const isAutoMessage = msg.is_auto === true || msg.is_auto === 1;

                // Determine message class based on sender type and auto status
                let messageClass = '';
                if (isAutoMessage) {
                    messageClass = 'auto-message';
                }

                // Create message element
                const messageElement = document.createElement('div');
                messageElement.className = `message ${isOutgoing ? 'message-outgoing' : 'message-incoming'}`;
                messageElement.setAttribute('data-message-id', msg.id);
                // Store original timestamp for debugging
                messageElement.setAttribute('data-timestamp', msg.timestamp);
                messageElement.innerHTML = `
                    <div class="message-bubble ${messageClass}">
                        ${messageContent}
                        <div class="message-time">
                            ${messageTime}
                            ${isOutgoing ? `<span class="message-status">${getStatusIcon(msg.status || 'sent')}</span>` : ''}
                        </div>
                    </div>
                    <button class="reaction-button" onclick="showReactionPicker(event, '${msg.id}')" title="Add reaction">
                        <i class="far fa-smile"></i>
                    </button>
                    <div class="message-reactions" id="reactions-${msg.id}">
                        <!-- Reactions will be loaded automatically -->
                    </div>
                    <div class="reaction-picker" id="reaction-picker-${msg.id}" style="display: none;">
                        <button onclick="addReaction('${msg.id}', '👍')" title="Like">👍</button>
                        <button onclick="addReaction('${msg.id}', '❤️')" title="Love">❤️</button>
                        <button onclick="addReaction('${msg.id}', '😂')" title="Laugh">😂</button>
                        <button onclick="addReaction('${msg.id}', '😮')" title="Wow">😮</button>
                        <button onclick="addReaction('${msg.id}', '😢')" title="Sad">😢</button>
                        <button onclick="addReaction('${msg.id}', '👏')" title="Applause">👏</button>
                    </div>
                `;

                fragment.appendChild(messageElement);

                // Fetch reactions for all non-temporary messages
                if (!msg.id.toString().startsWith('temp_')) {
                    // Only fetch reactions if we're not preserving them or if this message doesn't have preserved reactions
                    if (!preserveReactions || !existingReactions[msg.id]) {
                        // Fetch reactions for this message
                        fetchReactions(msg.id);

                        // If we already have reactions data, update the UI immediately while we wait for fresh data
                        if (msg.reactions && msg.reactions.length > 0) {
                            updateReactionsUI(msg.id, msg.reactions);
                        }
                    }
                }
            });

            // Since we cleared the container, just append all messages
            messagesContainer.appendChild(fragment);

            // Handle scroll position
            if (preserveScroll && scrollPosition > 0) {
                // If we're loading older messages, maintain the specified scroll position
                messagesContainer.scrollTop = scrollPosition;
            } else if (scrolledToBottom) {
                // If we were at the bottom before, scroll to bottom again
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            // Always restore reactions for existing messages
            // Wait for DOM to update
            setTimeout(() => {
                // Restore saved reactions
                for (const [messageId, reactionsHtml] of Object.entries(existingReactions)) {
                    const container = document.getElementById(`reactions-${messageId}`);
                    if (container) {
                        // Only restore if the container is empty or we're explicitly preserving reactions
                        if (container.innerHTML.trim() === '' || preserveReactions) {
                            container.innerHTML = reactionsHtml;
                            container.style.display = 'flex';
                            container.style.visibility = 'visible';
                            container.style.opacity = '1';

                            // Make sure all reaction items inside are visible
                            const reactionItems = container.querySelectorAll('.reaction-item');
                            reactionItems.forEach(item => {
                                item.style.display = 'inline-flex !important';
                                item.style.visibility = 'visible !important';
                                item.style.opacity = '1 !important';
                            });

                            // Skip fetching reactions for this message since we're restoring them
                            reactionsCache.timestamp[messageId] = Date.now();

                            // Log that we restored reactions
                            console.log(`Restored reactions for message ${messageId}`);
                        }
                    }
                }

                // Force all reaction containers to be visible
                document.querySelectorAll('.message-reactions').forEach(container => {
                    container.style.display = 'flex';
                    container.style.visibility = 'visible';
                    container.style.opacity = '1';
                });
            }, 0);

            // Handle scroll position
            if (preserveScroll) {
                // Maintain relative scroll position
                const newScrollHeight = messagesContainer.scrollHeight;
                const scrollDiff = newScrollHeight - oldScrollHeight;
                messagesContainer.scrollTop = scrollDiff;
            } else {
                // Scroll to bottom for new messages
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            // Add scroll event listener to detect when user scrolls to top
            if (!messagesContainer.hasScrollListener) {
                messagesContainer.addEventListener('scroll', function() {
                    // If user scrolls near the top (within 100px) and we have more messages
                    if (messagesContainer.scrollTop < 100 &&
                        conversations[contactId] &&
                        conversations[contactId].metadata &&
                        conversations[contactId].metadata.hasMore &&
                        !conversations[contactId].metadata.isLoading) {

                        // Load more messages
                        if (conversations[contactId].length > 0) {
                            const firstMessageId = conversations[contactId][0].id;
                            loadMessagesForContact(contactId, { beforeId: firstMessageId });
                        }
                    }
                });
                messagesContainer.hasScrollListener = true;
            }

        }

        // Function to append a new message without rebuilding the entire chat
        function appendNewMessage(msg, contactId) {
            // Only append if this is the current conversation
            if (currentReceiverId !== contactId) {
                return;
            }

            const messagesContainer = document.getElementById('chat-messages');
            const isOutgoing = msg.sender_id == {{ session.get('user_id', 0) }};
            const messageTime = formatMessageTime(msg.timestamp);

            // Check if we need to add a date separator
            const messageDate = new Date(msg.timestamp).toLocaleDateString();
            const lastMessage = messagesContainer.querySelector('.message:last-child');
            let needsDateSeparator = false;

            if (lastMessage) {
                const lastMessageTimestamp = lastMessage.getAttribute('data-timestamp');
                if (lastMessageTimestamp) {
                    const lastMessageDate = new Date(lastMessageTimestamp).toLocaleDateString();
                    needsDateSeparator = messageDate !== lastMessageDate;
                }
            } else {
                needsDateSeparator = true;
            }

            // Add date separator if needed
            if (needsDateSeparator) {
                const dateSeparator = document.createElement('div');
                dateSeparator.className = 'system-message';
                dateSeparator.innerHTML = `<span>${messageDate}</span>`;
                messagesContainer.appendChild(dateSeparator);
            }

            // Handle different message types
            let messageContent = '';

            if (msg.is_deleted) {
                messageContent = '<em class="text-muted">This message was deleted</em>';
            } else if (msg.message_type === 'file') {
                const timestamp = new Date().getTime();
                const fileUrl = msg.file_url || `/api/files/${msg.id}?t=${timestamp}`;

                // For temporary messages, show preview for images or icon for other files
                if (msg.id.toString().startsWith('temp_')) {
                    if (msg.file_mime_type && msg.file_mime_type.startsWith('image/')) {
                        if (msg.file_url) {
                            messageContent = `
                                <div class="message-image">
                                    <img src="${msg.file_url}" alt="${msg.file_name}" class="img-fluid rounded"
                                         onerror="this.onerror=null; this.src='/static/img/image-placeholder.png';">
                                    <div class="mt-1 small">${msg.file_name} <span class="ms-2">(Sending...)</span></div>
                                </div>
                            `;
                        } else {
                            messageContent = `
                                <div class="message-file">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-image me-2"></i>
                                        <span>${msg.file_name}</span>
                                        <span class="ms-2 text-muted small">(Sending...)</span>
                                    </div>
                                </div>
                            `;
                        }
                    } else {
                        let fileIcon = 'fa-file';
                        if (msg.file_mime_type) {
                            if (msg.file_mime_type.includes('pdf')) fileIcon = 'fa-file-pdf';
                            else if (msg.file_mime_type.includes('word') || msg.file_mime_type.includes('document')) fileIcon = 'fa-file-word';
                            else if (msg.file_mime_type.includes('excel') || msg.file_mime_type.includes('sheet')) fileIcon = 'fa-file-excel';
                            else if (msg.file_mime_type.includes('zip') || msg.file_mime_type.includes('archive')) fileIcon = 'fa-file-archive';
                            else if (msg.file_mime_type.includes('audio')) fileIcon = 'fa-file-audio';
                            else if (msg.file_mime_type.includes('video')) fileIcon = 'fa-file-video';
                        }

                        messageContent = `
                            <div class="message-file">
                                <div class="d-flex align-items-center">
                                    <i class="fas ${fileIcon} me-2"></i>
                                    <span>${msg.file_name}</span>
                                    <span class="ms-2 text-muted small">(Sending...)</span>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // For non-temporary messages
                    if (msg.file_mime_type && msg.file_mime_type.startsWith('image/')) {
                        messageContent = `
                            <div class="message-image">
                                <img src="${fileUrl}" alt="${msg.file_name}" class="img-fluid rounded"
                                     onerror="this.onerror=null; this.src='/static/img/image-placeholder.png';">
                                <div class="mt-1 small">${msg.file_name}</div>
                            </div>
                        `;
                    } else {
                        let fileIcon = 'fa-file';
                        if (msg.file_mime_type) {
                            if (msg.file_mime_type.includes('pdf')) fileIcon = 'fa-file-pdf';
                            else if (msg.file_mime_type.includes('word') || msg.file_mime_type.includes('document')) fileIcon = 'fa-file-word';
                            else if (msg.file_mime_type.includes('excel') || msg.file_mime_type.includes('sheet')) fileIcon = 'fa-file-excel';
                            else if (msg.file_mime_type.includes('zip') || msg.file_mime_type.includes('archive')) fileIcon = 'fa-file-archive';
                            else if (msg.file_mime_type.includes('audio')) fileIcon = 'fa-file-audio';
                            else if (msg.file_mime_type.includes('video')) fileIcon = 'fa-file-video';
                        }

                        messageContent = `
                            <div class="message-file">
                                <a href="${fileUrl}" target="_blank" class="d-flex align-items-center text-decoration-none">
                                    <i class="fas ${fileIcon} me-2"></i>
                                    <span>${msg.file_name}</span>
                                </a>
                            </div>
                        `;
                    }
                }
            } else {
                // Text message
                messageContent = msg.message || '';
                // Convert URLs to clickable links
                messageContent = messageContent.replace(
                    /(https?:\/\/[^\s]+)/g,
                    '<a href="$1" target="_blank" class="text-decoration-underline">$1</a>'
                );
            }

            // Determine if this is an auto message
            const isAutoMessage = msg.is_auto === true || msg.is_auto === 1;
            let messageClass = isAutoMessage ? 'auto-message' : '';

            // Create message element
            const messageElement = document.createElement('div');
            messageElement.className = `message ${isOutgoing ? 'message-outgoing' : 'message-incoming'}`;
            messageElement.setAttribute('data-message-id', msg.id);
            messageElement.setAttribute('data-timestamp', msg.timestamp);
            messageElement.innerHTML = `
                <div class="message-bubble ${messageClass}">
                    ${messageContent}
                    <div class="message-time">
                        ${messageTime}
                        ${isOutgoing ? `<span class="message-status">${getStatusIcon(msg.status || 'sent')}</span>` : ''}
                    </div>
                </div>
                <button class="reaction-button" onclick="showReactionPicker(event, '${msg.id}')" title="Add reaction">
                    <i class="far fa-smile"></i>
                </button>
                <div class="message-reactions" id="reactions-${msg.id}">
                    <!-- Reactions will be loaded automatically -->
                </div>
                <div class="reaction-picker" id="reaction-picker-${msg.id}" style="display: none;">
                    <button onclick="addReaction('${msg.id}', '👍')" title="Like">👍</button>
                    <button onclick="addReaction('${msg.id}', '❤️')" title="Love">❤️</button>
                    <button onclick="addReaction('${msg.id}', '😂')" title="Laugh">😂</button>
                    <button onclick="addReaction('${msg.id}', '😮')" title="Wow">😮</button>
                    <button onclick="addReaction('${msg.id}', '😢')" title="Sad">😢</button>
                    <button onclick="addReaction('${msg.id}', '👏')" title="Applause">👏</button>
                </div>
            `;

            // Append the message to the container
            messagesContainer.appendChild(messageElement);

            // Scroll to bottom to show the new message
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Fetch reactions for non-temporary messages
            if (!msg.id.toString().startsWith('temp_')) {
                fetchReactions(msg.id);
            }
        }

        // Function to update a temporary message with the real message data
        function updateTemporaryMessage(data) {
            const tempMessageElement = document.querySelector(`.message[data-message-id="temp_${data.id}"]`);
            if (!tempMessageElement) {
                return;
            }

            // Update the message ID to the real ID
            tempMessageElement.setAttribute('data-message-id', data.id);
            tempMessageElement.setAttribute('data-timestamp', data.timestamp);

            // Update the message content if it's a file message
            if (data.message_type === 'file') {
                const messageContent = tempMessageElement.querySelector('.message-bubble');
                const timestamp = new Date().getTime();
                const fileUrl = data.file_url || `/api/files/${data.id}?t=${timestamp}`;

                let newContent = '';
                if (data.file_mime_type && data.file_mime_type.startsWith('image/')) {
                    newContent = `
                        <div class="message-image">
                            <img src="${fileUrl}" alt="${data.file_name}" class="img-fluid rounded"
                                 onerror="this.onerror=null; this.src='/static/img/image-placeholder.png';">
                            <div class="mt-1 small">${data.file_name}</div>
                        </div>
                    `;
                } else {
                    let fileIcon = 'fa-file';
                    if (data.file_mime_type) {
                        if (data.file_mime_type.includes('pdf')) fileIcon = 'fa-file-pdf';
                        else if (data.file_mime_type.includes('word') || data.file_mime_type.includes('document')) fileIcon = 'fa-file-word';
                        else if (data.file_mime_type.includes('excel') || data.file_mime_type.includes('sheet')) fileIcon = 'fa-file-excel';
                        else if (data.file_mime_type.includes('zip') || data.file_mime_type.includes('archive')) fileIcon = 'fa-file-archive';
                        else if (data.file_mime_type.includes('audio')) fileIcon = 'fa-file-audio';
                        else if (data.file_mime_type.includes('video')) fileIcon = 'fa-file-video';
                    }

                    newContent = `
                        <div class="message-file">
                            <a href="${fileUrl}" target="_blank" class="d-flex align-items-center text-decoration-none">
                                <i class="fas ${fileIcon} me-2"></i>
                                <span>${data.file_name}</span>
                            </a>
                        </div>
                    `;
                }

                // Update the message time
                const messageTime = formatMessageTime(data.timestamp);
                const isOutgoing = data.sender_id == {{ session.get('user_id', 0) }};

                messageContent.innerHTML = `
                    ${newContent}
                    <div class="message-time">
                        ${messageTime}
                        ${isOutgoing ? `<span class="message-status">${getStatusIcon(data.status || 'sent')}</span>` : ''}
                    </div>
                `;
            }

            // Update reaction picker and reactions container IDs
            const reactionPicker = tempMessageElement.querySelector('.reaction-picker');
            if (reactionPicker) {
                reactionPicker.id = `reaction-picker-${data.id}`;
            }

            const reactionsContainer = tempMessageElement.querySelector('.message-reactions');
            if (reactionsContainer) {
                reactionsContainer.id = `reactions-${data.id}`;
            }

            const reactionButton = tempMessageElement.querySelector('.reaction-button');
            if (reactionButton) {
                reactionButton.setAttribute('onclick', `showReactionPicker(event, '${data.id}')`);
            }

            // Update reaction picker buttons
            const reactionButtons = tempMessageElement.querySelectorAll('.reaction-picker button');
            reactionButtons.forEach(button => {
                const emoji = button.textContent.trim();
                button.setAttribute('onclick', `addReaction('${data.id}', '${emoji}')`);
            });

            // Fetch reactions for the real message
            fetchReactions(data.id);
        }

        // Cache for reactions to avoid repeated fetches
        const reactionsCache = {
            data: {},
            timestamp: {},
            pendingFetches: new Set(),
            batchFetchQueue: [],
            batchTimer: null,
            cacheExpiration: 60 * 1000 // 60 seconds
        };

        // Function to fetch reactions for a message
        function fetchReactions(messageId) {
            // Check if the message element exists in the DOM
            const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);
            const isVisible = !!messageElement;

            // Check if reactions container already has content
            const reactionsContainer = document.getElementById(`reactions-${messageId}`);
            const hasExistingReactions = reactionsContainer && reactionsContainer.innerHTML.trim() !== '';

            // If reactions are already displayed, don't refresh them
            if (hasExistingReactions) {
                // Update cache timestamp to prevent other functions from refreshing
                reactionsCache.timestamp[messageId] = Date.now();
                return;
            }

            // Check if we already have this reaction in cache and it's not expired
            const now = Date.now();
            const cacheTime = reactionsCache.timestamp[messageId] || 0;
            const cacheExpired = (now - cacheTime >= reactionsCache.cacheExpiration);

            // If we have cached data and it's not expired, use it immediately
            if (reactionsCache.data[messageId] && !cacheExpired) {
                // Don't update the UI to avoid refreshing existing reactions
                // This prevents existing reactions from disappearing

                // If the message is not visible, don't fetch fresh data
                if (!isVisible) {
                    return;
                }
            }

            // If this message is already being fetched, don't fetch again
            if (reactionsCache.pendingFetches.has(messageId)) {
                return;
            }

            // Add to pending fetches
            reactionsCache.pendingFetches.add(messageId);

            // Add to batch queue
            reactionsCache.batchFetchQueue.push(messageId);

            // Set up batch timer if not already running
            if (!reactionsCache.batchTimer) {
                reactionsCache.batchTimer = setTimeout(() => {
                    batchFetchReactions();
                }, 50); // Wait 50ms to batch requests
            }
        }

        // Function to batch fetch reactions for multiple messages
        function batchFetchReactions() {
            // Clear the timer
            reactionsCache.batchTimer = null;

            // Get the batch of message IDs to fetch (max 20 at a time)
            const messageIds = reactionsCache.batchFetchQueue.splice(0, 20);

            if (messageIds.length === 0) {
                return;
            }

            console.log(`Batch fetching reactions for ${messageIds.length} messages`);

            // Fetch reactions for all messages in the batch
            fetch(`/api/messages/batch/reactions?ids=${messageIds.join(',')}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.reactions) {
                        console.log('Successfully fetched reactions:', data.reactions);
                        // Process each message's reactions
                        Object.entries(data.reactions).forEach(([msgId, reactions]) => {
                            // Update cache
                            reactionsCache.data[msgId] = reactions;
                            reactionsCache.timestamp[msgId] = Date.now();

                            // Remove from pending fetches
                            reactionsCache.pendingFetches.delete(msgId);

                            // Update UI
                            updateReactionsUI(msgId, reactions);
                            console.log(`Updated reactions UI for message ${msgId}:`, reactions);
                        });
                    } else {
                        console.error(`Error batch fetching reactions:`, data.error);

                        // Remove all from pending fetches
                        messageIds.forEach(id => reactionsCache.pendingFetches.delete(id));
                    }

                    // If there are more in the queue, process them
                    if (reactionsCache.batchFetchQueue.length > 0) {
                        reactionsCache.batchTimer = setTimeout(() => {
                            batchFetchReactions();
                        }, 50);
                    }
                })
                .catch(error => {
                    console.error(`Error batch fetching reactions:`, error);

                    // Remove all from pending fetches
                    messageIds.forEach(id => reactionsCache.pendingFetches.delete(id));

                    // If there are more in the queue, process them
                    if (reactionsCache.batchFetchQueue.length > 0) {
                        reactionsCache.batchTimer = setTimeout(() => {
                            batchFetchReactions();
                        }, 50);
                    }
                });
        }

        // Fallback for single message reaction fetch (for backward compatibility)
        function fetchSingleMessageReactions(messageId) {
            fetch(`/api/messages/${messageId}/reactions`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update cache
                        reactionsCache.data[messageId] = data.reactions;
                        reactionsCache.timestamp[messageId] = Date.now();

                        // Update UI
                        updateReactionsUI(messageId, data.reactions);
                    } else {
                        console.error(`Error fetching reactions:`, data.error);
                    }
                })
                .catch(error => {
                    console.error(`Error fetching reactions:`, error);
                });
        }

        // Function to update the UI with reactions data
        function updateReactionsUI(messageId, reactions) {
            const reactionsContainer = document.getElementById(`reactions-${messageId}`);
            if (!reactionsContainer) return;

            // Clear existing reactions
            reactionsContainer.innerHTML = '';

            // Group reactions by emoji
            const groupedReactions = {};
            const currentUserId = {{ session.get('user_id', 0) }};

            // Even if no reactions, keep container visible but clear it
            if (!reactions || reactions.length === 0) {
                reactionsContainer.innerHTML = '';
                reactionsContainer.style.display = 'flex';
                reactionsContainer.style.visibility = 'visible';
                reactionsContainer.style.opacity = '1';
                return;
            }

            // Sort reactions by timestamp (newest first)
            const sortedReactions = [...reactions].sort((a, b) => {
                return new Date(b.created_at) - new Date(a.created_at);
            });

            // Process reactions - only count one reaction per user per emoji
            const userEmojiMap = new Map(); // Track which users reacted with which emojis

            sortedReactions.forEach(reaction => {
                // Skip if this user already reacted with any emoji (one reaction per user)
                const userKey = `${reaction.user_id}`;

                // If this user hasn't reacted yet, or we're updating their reaction
                if (!userEmojiMap.has(userKey) || userEmojiMap.get(userKey) === reaction.emoji) {
                    userEmojiMap.set(userKey, reaction.emoji);

                    if (!groupedReactions[reaction.emoji]) {
                        groupedReactions[reaction.emoji] = {
                            userReacted: false,
                            timestamp: reaction.created_at, // Store the most recent timestamp
                            users: [] // Store user IDs who reacted with this emoji
                        };
                    }

                    // Only add user if not already in the list
                    if (!groupedReactions[reaction.emoji].users.includes(reaction.user_id)) {
                        groupedReactions[reaction.emoji].users.push(reaction.user_id);
                    }

                    // Check if current user reacted with this emoji
                    if (reaction.user_id == currentUserId) {
                        groupedReactions[reaction.emoji].userReacted = true;
                    }
                }
            });

            // Create reaction items
            for (const [emoji, info] of Object.entries(groupedReactions)) {
                // Check if this reaction already exists in the DOM
                const existingReaction = reactionsContainer.querySelector(`.reaction-item[data-emoji="${emoji}"]`);
                const isNewReaction = !existingReaction;

                const reactionItem = document.createElement('div');
                reactionItem.className = `reaction-item ${info.userReacted ? 'active' : ''} ${isNewReaction ? 'reaction-item-new' : ''}`;
                reactionItem.setAttribute('data-emoji', emoji);
                reactionItem.setAttribute('data-timestamp', info.timestamp);
                // Force visibility with !important to override any CSS that might hide it
                reactionItem.style.display = 'inline-flex !important';
                reactionItem.style.visibility = 'visible !important';
                reactionItem.style.opacity = '1 !important';

                // Format the timestamp
                const reactionTime = formatReactionTime(info.timestamp);

                reactionItem.innerHTML = `
                    <span class="reaction-emoji">${emoji}</span>
                    <span class="reaction-time" title="${new Date(info.timestamp).toLocaleString()}">${reactionTime}</span>
                `;

                // Add click handler
                reactionItem.onclick = function() {
                    if (info.userReacted) {
                        removeReaction(messageId, emoji);
                    } else {
                        addReaction(messageId, emoji);
                    }
                };

                reactionsContainer.appendChild(reactionItem);
            }

            // Always make the container visible, even if empty
            reactionsContainer.style.display = 'flex';
            reactionsContainer.style.visibility = 'visible';
            reactionsContainer.style.opacity = '1';
        }



        // Format reaction time to show how long ago it was added
        function formatReactionTime(timestamp) {
            const now = new Date();
            const reactionTime = new Date(timestamp);
            const diffMs = now - reactionTime;

            // Convert to seconds
            const diffSec = Math.floor(diffMs / 1000);

            if (diffSec < 60) {
                return 'just now';
            } else if (diffSec < 3600) {
                const mins = Math.floor(diffSec / 60);
                return `${mins}m ago`;
            } else if (diffSec < 86400) {
                const hours = Math.floor(diffSec / 3600);
                return `${hours}h ago`;
            } else {
                const days = Math.floor(diffSec / 86400);
                return `${days}d ago`;
            }
        }

        // Function to handle file selection
        function setupFileUpload() {
            const fileUploadBtn = document.getElementById('message-file-upload-btn');
            const fileInput = document.getElementById('file-input');
            const filePreviewContainer = document.getElementById('file-preview-container');
            const fileNameElement = document.getElementById('file-name');
            const fileIconElement = document.getElementById('file-icon');

            if (!fileUploadBtn || !fileInput || !filePreviewContainer || !fileNameElement || !fileIconElement) {
                console.error('File upload elements not found');
                return;
            }

            console.log('Setting up file upload with button:', fileUploadBtn);

            // When file upload button is clicked, trigger file input
            fileUploadBtn.addEventListener('click', function() {
                console.log('File upload button clicked');
                fileInput.click();
            });

            // When a file is selected
            fileInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (!file) return;

                // Check file size (5MB max)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    alert('File size exceeds 5MB limit. Please choose a smaller file.');
                    fileInput.value = '';
                    return;
                }

                // Update preview
                fileNameElement.textContent = file.name;

                // Set appropriate icon based on file type
                if (file.type.startsWith('image/')) {
                    fileIconElement.className = 'fas fa-image me-2';

                    // Create image preview for images
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Create or update image preview
                        let imgPreview = document.getElementById('file-image-preview');
                        if (!imgPreview) {
                            imgPreview = document.createElement('div');
                            imgPreview.id = 'file-image-preview';
                            imgPreview.className = 'mt-2';
                            filePreviewContainer.appendChild(imgPreview);
                        }

                        imgPreview.innerHTML = `
                            <img src="${e.target.result}" alt="${file.name}"
                                 class="img-fluid rounded" style="max-height: 100px; max-width: 100%;">
                        `;
                    };
                    reader.readAsDataURL(file);
                } else if (file.type.startsWith('video/')) {
                    fileIconElement.className = 'fas fa-video me-2';
                } else if (file.type.startsWith('audio/')) {
                    fileIconElement.className = 'fas fa-music me-2';
                } else if (file.type.includes('pdf')) {
                    fileIconElement.className = 'fas fa-file-pdf me-2';
                } else if (file.type.includes('word') || file.type.includes('document')) {
                    fileIconElement.className = 'fas fa-file-word me-2';
                } else if (file.type.includes('excel') || file.type.includes('sheet')) {
                    fileIconElement.className = 'fas fa-file-excel me-2';
                } else {
                    fileIconElement.className = 'fas fa-file me-2';
                }

                // Show preview container
                filePreviewContainer.style.display = 'block';

                // Update message input placeholder to show selected file
                const messageInput = document.getElementById('message-input');
                if (messageInput) {
                    // Save original placeholder if not already saved
                    if (!messageInput.getAttribute('data-original-placeholder')) {
                        messageInput.setAttribute('data-original-placeholder', messageInput.placeholder);
                    }

                    // Update placeholder with file name
                    messageInput.placeholder = `Send "${file.name}" or add a message`;

                    // Add a visual indicator that a file is selected
                    messageInput.classList.add('file-selected');
                }
            });
        }

        // Function to cancel file upload
        function cancelFileUpload() {
            const fileInput = document.getElementById('file-input');
            const filePreviewContainer = document.getElementById('file-preview-container');
            const messageInput = document.getElementById('message-input');
            const imgPreview = document.getElementById('file-image-preview');

            // Reset file input and hide preview
            fileInput.value = '';
            filePreviewContainer.style.display = 'none';

            // Remove image preview if it exists
            if (imgPreview) {
                imgPreview.innerHTML = '';
            }

            // Reset message input placeholder to original
            if (messageInput) {
                const originalPlaceholder = messageInput.getAttribute('data-original-placeholder');
                if (originalPlaceholder) {
                    messageInput.placeholder = originalPlaceholder;
                } else {
                    messageInput.placeholder = 'Type a message...';
                }

                // Remove visual indicator
                messageInput.classList.remove('file-selected');
            }
        }

        // Function to send message
        function sendMessage() {
            const receiverId = parseInt(document.getElementById('receiver-id').value);
            const messageInput = document.getElementById('message-input');
            const fileInput = document.getElementById('file-input');
            const message = messageInput.value.trim();
            const file = fileInput ? fileInput.files[0] : null;

            // If neither message nor file, do nothing
            if (!message && !file) {
                return;
            }

            if (!receiverId) {
                return;
            }

            // Get the selected contact
            const selectedContact = contacts.find(c => c.id === receiverId);

            // Check if this is related to a job application
            let relatedToJobId = null;
            let relatedToApplicationId = null;

            // If there are existing messages, check if they're related to a job
            if (conversations[receiverId] && conversations[receiverId].length > 0) {
                // Find the first message with job relation
                const jobRelatedMsg = conversations[receiverId].find(
                    msg => msg.related_to_job_id || msg.related_to_application_id
                );

                if (jobRelatedMsg) {
                    relatedToJobId = jobRelatedMsg.related_to_job_id;
                    relatedToApplicationId = jobRelatedMsg.related_to_application_id;
                }
            }

            // Create a formatted timestamp for display
            const now = new Date();
            const formattedTimestamp = now.toISOString();

            // Get current user ID from session
            const currentUserId = {{ session.get('user_id', 0) }};
            const currentUserType = '{{ session.get("user_type", "") }}';

            // If we have a file, send it
            if (file) {
                // Create a FileReader to read the file as base64
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Get base64 data (remove the data URL prefix)
                    const base64Data = e.target.result.split(',')[1];

                    // Create a temporary message for immediate display
                    const tempMessage = {
                        id: 'temp_' + Date.now(),
                        sender_id: currentUserId,
                        receiver_id: receiverId,
                        message: file.type.startsWith('image/') ? 'Sent a photo' : 'Sent a file',
                        timestamp: formattedTimestamp,
                        is_outgoing: true,
                        is_read: false,
                        status: 'sending',
                        sender_type: currentUserType,
                        is_auto: false,
                        message_type: 'file',
                        file_name: file.name,
                        file_mime_type: file.type,
                        file_url: e.target.result // Store the data URL for immediate display
                    };

                    // Add to local conversation array
                    if (!conversations[receiverId]) {
                        conversations[receiverId] = [];
                    }
                    conversations[receiverId].push(tempMessage);

                    // Update UI immediately - append new message instead of rebuilding all
                    appendNewMessage(tempMessage, receiverId);

                    // Update contact list with the new message
                    if (selectedContact) {
                        selectedContact.last_message_time = formattedTimestamp;
                        updateContactsList();
                    }

                    // Send file via Socket.IO
                    socket.emit('send_file_message', {
                        receiver_id: receiverId,
                        file_data: base64Data,
                        file_name: file.name,
                        file_mime_type: file.type,
                        related_to_job_id: relatedToJobId,
                        related_to_application_id: relatedToApplicationId
                    });

                    // Reset file input, hide preview, and reset placeholder
                    cancelFileUpload();
                };

                // Read the file as data URL (base64)
                reader.readAsDataURL(file);
            }
            // If we have a text message, send it
            else if (message) {
                // Create a temporary message with 'sending' status to show the clock icon
                const tempMessage = {
                    id: 'temp_' + Date.now(),
                    sender_id: currentUserId,
                    receiver_id: receiverId,
                    message: message,
                    timestamp: formattedTimestamp,
                    is_outgoing: true,
                    is_read: false,
                    status: 'sending',  // Use 'sending' status to show the clock icon
                    sender_type: currentUserType,
                    is_auto: false,
                    message_type: 'text'  // Explicitly set message type
                };

                // Add to local conversation array
                if (!conversations[receiverId]) {
                    conversations[receiverId] = [];
                }
                conversations[receiverId].push(tempMessage);

                // Update UI immediately but preserve existing reactions - append new message instead of rebuilding all
                appendNewMessage(tempMessage, receiverId);

                // Update contact list with the new message
                if (selectedContact) {
                    // Use the same formatted timestamp as the message
                    selectedContact.last_message_time = formattedTimestamp;
                    updateContactsList();
                }

                // Send message via Socket.IO
                socket.emit('send_message', {
                    receiver_id: receiverId,
                    message: message,
                    related_to_job_id: relatedToJobId,
                    related_to_application_id: relatedToApplicationId
                });

                // Clear input
                messageInput.value = '';
            }

            // Focus back on input
            messageInput.focus();
        }

        // Helper function to format time for contact list
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();

            // If today, show time
            if (date.toDateString() === now.toDateString()) {
                return formatTimeOnly(date);
            }

            // If this year, show month and day
            if (date.getFullYear() === now.getFullYear()) {
                return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
            }

            // Otherwise show date
            return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
        }

        // Helper function to format message time consistently
        function formatMessageTime(timestamp) {
            // Store the formatted time in a data attribute for debugging
            const date = new Date(timestamp);
            return formatTimeOnly(date);
        }

        // Helper function to get status icon
        function getStatusIcon(status) {
            switch (status) {
                case 'sending':
                    // Use a more visible clock icon for sending status
                    return '<i class="fas fa-clock" style="color: #6c757d;"></i>';
                case 'sent':
                    return '<i class="fas fa-check text-muted"></i>';
                case 'delivered':
                    return '<i class="fas fa-check-double text-muted"></i>';
                case 'read':
                    return '<i class="fas fa-check-double text-primary"></i>';
                default:
                    // Default to clock icon if status is unknown
                    return '<i class="fas fa-check text-muted"></i>';
            }
        }

        // Helper function to format time only (HH:MM) in a consistent way
        function formatTimeOnly(date) {
            // Get user's local time
            // Use padStart to ensure consistent 2-digit format
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        }

        // Helper function to convert UTC timestamp to local time
        function getLocalTime(timestamp) {
            // Parse the timestamp and convert to local time
            const date = new Date(timestamp);
            return date;
        }

        // Handle enter key in message input
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault(); // Prevent default to avoid newline
                sendMessage();
            }
        });

        // Add hover effect to send button
        document.getElementById('send-button').addEventListener('mouseover', function() {
            this.style.backgroundColor = 'var(--primary-pink)';
        });

        document.getElementById('send-button').addEventListener('mouseout', function() {
            this.style.backgroundColor = 'var(--primary-blue)';
        });

        // Message reactions functions
        function showReactionPicker(event, messageId) {
            event.stopPropagation();
            const picker = document.getElementById(`reaction-picker-${messageId}`);

            // Close all other pickers first
            document.querySelectorAll('.reaction-picker').forEach(p => {
                if (p.id !== `reaction-picker-${messageId}`) {
                    p.style.display = 'none';
                }
            });

            // Toggle this picker
            if (picker.style.display === 'none') {
                picker.style.display = 'flex';

                // Close when clicking outside
                const closePickerHandler = function(e) {
                    if (!picker.contains(e.target)) {
                        picker.style.display = 'none';
                        document.removeEventListener('click', closePickerHandler);
                    }
                };

                // Add the event listener with a slight delay to avoid immediate closing
                setTimeout(() => {
                    document.addEventListener('click', closePickerHandler);
                }, 10);
            } else {
                picker.style.display = 'none';
            }
        }

        function addReaction(messageId, emoji) {
            // Close the picker
            const picker = document.getElementById(`reaction-picker-${messageId}`);
            if (picker) {
                picker.style.display = 'none';
            }

            // Check if user already reacted with this emoji
            const reactionsContainer = document.getElementById(`reactions-${messageId}`);
            if (!reactionsContainer) return;

            const existingReaction = reactionsContainer.querySelector(`.reaction-item[data-emoji="${emoji}"].active`);

            // If user already reacted with this emoji, remove it instead
            if (existingReaction) {
                removeReaction(messageId, emoji);
                return;
            }

            // Check if user already reacted with a different emoji
            const userReaction = reactionsContainer.querySelector(`.reaction-item.active`);
            if (userReaction) {
                // Get the emoji of the existing reaction
                const existingEmoji = userReaction.getAttribute('data-emoji');
                // Remove the existing reaction first
                removeReaction(messageId, existingEmoji);
            }

            // Optimistically update the UI
            updateReactionUI(messageId, emoji, true);

            // First try to send via Socket.IO for real-time updates
            if (socketConnected) {
                console.log(`Sending reaction via Socket.IO: message=${messageId}, emoji=${emoji}`);
                socket.emit('add_reaction', {
                    message_id: messageId,
                    emoji: emoji
                });
            } else {
                // Fallback to REST API if Socket.IO is not connected
                console.log(`Socket not connected, using REST API for reaction: message=${messageId}, emoji=${emoji}`);
                fetch(`/api/messages/${messageId}/reactions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ emoji: emoji })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Refresh reactions to get the latest from server
                        fetchReactions(messageId);
                    } else {
                        console.error(`Error adding reaction:`, data.error);
                    }
                })
                .catch(error => {
                    console.error(`Error adding reaction:`, error);
                });
            }
        }

        function removeReaction(messageId, emoji) {
            // Optimistically update the UI
            updateReactionUI(messageId, emoji, false);

            // First try to send via Socket.IO for real-time updates
            if (socketConnected) {
                console.log(`Removing reaction via Socket.IO: message=${messageId}, emoji=${emoji}`);
                socket.emit('remove_reaction', {
                    message_id: messageId,
                    emoji: emoji
                });
            } else {
                // Fallback to REST API if Socket.IO is not connected
                console.log(`Socket not connected, using REST API to remove reaction: message=${messageId}, emoji=${emoji}`);
                fetch(`/api/messages/${messageId}/reactions/${encodeURIComponent(emoji)}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Refresh reactions to get the latest from server
                        fetchReactions(messageId);
                    } else {
                        console.error(`Error removing reaction:`, data.error);
                    }
                })
                .catch(error => {
                    console.error(`Error removing reaction:`, error);
                });
            }
        }

        function updateReactionUI(messageId, emoji, isAdding = true) {
            const reactionsContainer = document.getElementById(`reactions-${messageId}`);
            if (!reactionsContainer) return;

            // Check if this reaction already exists
            let reactionItem = reactionsContainer.querySelector(`.reaction-item[data-emoji="${emoji}"]`);

            // Check if user already has this reaction active
            const isAlreadyActive = reactionItem && reactionItem.classList.contains('active');

            if (reactionItem) {
                if (isAdding) {
                    // If adding and not already active, mark as active
                    if (!isAlreadyActive) {
                        reactionItem.classList.add('active');

                        // Update timestamp
                        const timeElement = reactionItem.querySelector('.reaction-time');
                        if (timeElement) {
                            const now = new Date();
                            const formattedTime = formatReactionTime(now.toISOString());
                            timeElement.textContent = formattedTime;
                            timeElement.title = now.toLocaleString();
                            reactionItem.setAttribute('data-timestamp', now.toISOString());
                        }
                    }
                } else {
                    // If removing and it was active, remove the entire reaction
                    if (isAlreadyActive) {
                        reactionItem.remove();
                    }
                }
            } else if (isAdding) {
                // Create new reaction item
                reactionItem = document.createElement('div');
                reactionItem.className = 'reaction-item active';
                reactionItem.setAttribute('data-emoji', emoji);

                // Add timestamp
                const now = new Date();
                const formattedTime = formatReactionTime(now.toISOString());
                reactionItem.setAttribute('data-timestamp', now.toISOString());

                reactionItem.innerHTML = `
                    <span class="reaction-emoji">${emoji}</span>
                    <span class="reaction-time" title="${now.toLocaleString()}">${formattedTime}</span>
                `;

                reactionItem.onclick = function() {
                    removeReaction(messageId, emoji);
                };

                // Make sure the reaction is visible
                reactionItem.style.display = 'inline-flex !important';
                reactionItem.style.visibility = 'visible !important';
                reactionItem.style.opacity = '1 !important';

                // Add the reaction to the container
                reactionsContainer.appendChild(reactionItem);
            }

            // Always keep the container visible, even if empty
            reactionsContainer.style.display = 'flex';
            reactionsContainer.style.visibility = 'visible';
            reactionsContainer.style.opacity = '1';
        }





        // Unified Notification System - Matching genius_page.html
        function showUnifiedNotification(options) {
            const {
                message,
                type = 'info',
                title = null,
                duration = 3000,
                clickAction = null,
                position = 'bottom-right',
                showClose = true,
                persistent = false
            } = options;

            const notification = document.createElement('div');
            notification.className = `unified-notification ${type}`;

            // Add icon based on type
            let icon = '';
            switch(type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-triangle"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-circle"></i>';
                    break;
                case 'application':
                    icon = '<i class="fas fa-file-alt"></i>';
                    break;
                case 'contract':
                    icon = '<i class="fas fa-file-contract"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-bell"></i>';
            }

            // Build notification content
            let content = `
                <div class="notification-icon">${icon}</div>
                <div class="notification-content">
                    ${title ? `<div class="notification-title">${title}</div>` : ''}
                    <div class="notification-message">${message}</div>
                </div>
            `;

            if (showClose) {
                content += `<button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>`;
            }

            notification.innerHTML = content;

            // Add click action if provided
            if (clickAction) {
                notification.style.cursor = 'pointer';
                notification.addEventListener('click', function(e) {
                    if (!e.target.closest('.notification-close')) {
                        clickAction();
                        notification.remove();
                    }
                });
            }

            // Position the notification
            setNotificationPosition(notification, position);

            document.body.appendChild(notification);

            // Show the notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto-hide if not persistent
            if (!persistent && duration > 0) {
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }, duration);
            }

            return notification;
        }

        function setNotificationPosition(notification, position) {
            // Reset all position styles
            notification.style.top = 'auto';
            notification.style.bottom = 'auto';
            notification.style.left = 'auto';
            notification.style.right = 'auto';

            switch(position) {
                case 'top-right':
                    notification.style.top = '30px';
                    notification.style.right = '30px';
                    break;
                case 'top-left':
                    notification.style.top = '30px';
                    notification.style.left = '30px';
                    break;
                case 'bottom-left':
                    notification.style.bottom = '30px';
                    notification.style.left = '30px';
                    break;
                case 'center':
                    notification.style.top = '50%';
                    notification.style.left = '50%';
                    notification.style.transform = 'translate(-50%, -50%)';
                    break;
                default: // bottom-right
                    notification.style.bottom = '30px';
                    notification.style.right = '30px';
            }
        }

        function showSystemNotification(message, type = 'info') {
            return showUnifiedNotification({
                message: message,
                type: type,
                duration: 3000
            });
        }

        // Real-time notification badge management
        let notificationCount = 0;

        function updateNotificationBadge(count = null) {
            const badge = document.getElementById('notification-count');
            const bell = document.getElementById('notification-bell');

            if (count !== null) {
                notificationCount = count;
            }

            if (badge) {
                if (notificationCount > 0) {
                    badge.textContent = notificationCount > 99 ? '99+' : notificationCount;
                    badge.style.display = 'flex';
                    badge.classList.add('new');

                    // Remove animation class after animation completes
                    setTimeout(() => {
                        badge.classList.remove('new');
                    }, 600);
                } else {
                    badge.style.display = 'none';
                }
            }

            // Add bell shake animation for new notifications
            if (bell && count > 0) {
                bell.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    bell.style.animation = '';
                }, 500);
            }
        }

        function incrementNotificationBadge() {
            notificationCount++;
            updateNotificationBadge();
        }

        function decrementNotificationBadge() {
            if (notificationCount > 0) {
                notificationCount--;
                updateNotificationBadge();
            }
        }

        function resetNotificationBadge() {
            notificationCount = 0;
            updateNotificationBadge();
        }

        // Initialize notification system
        function initializeNotificationSystem() {
            console.log('🔔 Initializing notification system');

            // Initialize notification badge
            updateNotificationBadge();

            // Add notification bell click handler
            const notificationBell = document.getElementById('notification-bell');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationBell && notificationDropdown) {
                notificationBell.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // Toggle dropdown - Simple display toggle like genius_page.html
                    if (notificationDropdown.style.display === 'block') {
                        notificationDropdown.style.display = 'none';
                    } else {
                        notificationDropdown.style.display = 'block';
                        loadNotifications();

                        // Reset notification badge when user opens dropdown (reads notifications)
                        setTimeout(() => {
                            resetNotificationBadge();
                        }, 1000); // Small delay to let user see the notifications
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!notificationBell.contains(e.target) && !notificationDropdown.contains(e.target)) {
                        notificationDropdown.style.display = 'none';
                    }
                });
            }

            // Add mark all as read handler
            const markAllRead = document.getElementById('mark-all-read');
            if (markAllRead) {
                markAllRead.addEventListener('click', function() {
                    resetNotificationBadge();

                    // Show confirmation
                    showUnifiedNotification({
                        message: 'All notifications marked as read',
                        type: 'info',
                        duration: 2000,
                        position: 'bottom-right'
                    });

                    // Close dropdown
                    if (notificationDropdown) {
                        notificationDropdown.style.display = 'none';
                    }
                });
            }
        }

        function loadNotifications() {
            // Determine the correct endpoint based on user type
            const userType = '{{ session.get("user_type", "") }}';
            let notificationEndpoint;

            console.log('🔔 loadNotifications() called for user type:', userType);

            if (userType === 'client') {
                notificationEndpoint = '/get_client_notifications';
            } else if (userType === 'genius') {
                notificationEndpoint = '/get_notification_history';
            } else {
                console.error('❌ Unknown user type:', userType);
                return;
            }

            console.log('📡 Loading notifications for user type:', userType, 'using endpoint:', notificationEndpoint);

            // Load notifications from the appropriate endpoint
            fetch(notificationEndpoint)
                .then(response => {
                    console.log('📡 Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📋 Notification data received:', data);
                    console.log('📊 Data structure:', {
                        success: data.success,
                        notificationsArray: data.notifications,
                        notificationsLength: data.notifications ? data.notifications.length : 'undefined',
                        firstNotification: data.notifications && data.notifications.length > 0 ? data.notifications[0] : 'none'
                    });

                    const notificationList = document.getElementById('notification-list');
                    const emptyNotifications = document.getElementById('empty-notifications');

                    if (data.success && data.notifications && data.notifications.length > 0) {
                        console.log('✅ Found', data.notifications.length, 'notifications');
                        notificationList.innerHTML = '';
                        emptyNotifications.style.display = 'none';

                        // Count unread notifications
                        let unreadCount = 0;

                        // Add each notification to the list (matching client_page.html implementation)
                        data.notifications.forEach((notification, index) => {
                            console.log(`📝 Processing notification ${index + 1}:`, notification);

                            if (notification.status === 'pending') {
                                unreadCount++;
                            }

                            const notificationItem = document.createElement('div');
                            notificationItem.className = `notification-item ${notification.status === 'pending' ? 'unread' : ''}`;
                            notificationItem.setAttribute('data-id', notification.id);

                            // Create notification content with enhanced styling (matching client_page.html)
                            let notificationContent = `
                                <div class="notification-icon-wrapper">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="notification-content">
                                    <p><strong>${notification.first_name} ${notification.last_name}</strong> applied for your job: <strong>${notification.job_title}</strong></p>
                                    <span>${formatTimeAgo(notification.created_at)}</span>
                            `;

                            // Add action buttons for pending applications
                            if (notification.status === 'pending') {
                                notificationContent += `
                                    <div class="notification-actions">
                                        <button class="accept-btn" data-id="${notification.id}">
                                            <i class="fas fa-check" style="margin-right: 5px;"></i> Accept
                                        </button>
                                        <button class="reject-btn" data-id="${notification.id}">
                                            <i class="fas fa-times" style="margin-right: 5px;"></i> Decline
                                        </button>
                                    </div>
                                `;
                            } else if (notification.status === 'accept') {
                                notificationContent += `
                                    <div class="notification-status accepted">
                                        <i class="fas fa-check-circle" style="margin-right: 4px;"></i> Accepted
                                    </div>
                                `;
                            } else if (notification.status === 'reject') {
                                notificationContent += `
                                    <div class="notification-status rejected">
                                        <i class="fas fa-times-circle" style="margin-right: 4px;"></i> Declined
                                    </div>
                                `;
                            }

                            notificationContent += `</div>`;
                            notificationItem.innerHTML = notificationContent;

                            // Add click event to view application details
                            notificationItem.addEventListener('click', function(e) {
                                // Don't navigate if clicking on buttons
                                if (e.target.closest('.notification-actions')) {
                                    return;
                                }

                                // Store proposal data for opening specific proposal
                                localStorage.setItem('openProposal', JSON.stringify({
                                    jobId: notification.job_id,
                                    jobTitle: notification.job_title,
                                    applicationId: notification.id
                                }));

                                // Redirect to allgigpost to view proposals
                                window.location.href = '/allgigpost';
                            });

                            notificationList.appendChild(notificationItem);
                        });

                        // Add event listeners for accept/reject buttons
                        document.querySelectorAll('.accept-btn').forEach(button => {
                            button.addEventListener('click', function(e) {
                                e.stopPropagation();
                                const applicationId = this.getAttribute('data-id');
                                handleApplication(applicationId, 'accept');
                            });
                        });

                        document.querySelectorAll('.reject-btn').forEach(button => {
                            button.addEventListener('click', function(e) {
                                e.stopPropagation();
                                const applicationId = this.getAttribute('data-id');
                                handleApplication(applicationId, 'reject');
                            });
                        });

                        console.log('🔢 Total unread notifications:', unreadCount);
                        // Update the notification badge with the actual count
                        updateNotificationBadge(unreadCount);
                    } else {
                        console.log('📭 No notifications found or API error');
                        notificationList.innerHTML = '';
                        emptyNotifications.style.display = 'block';
                        updateNotificationBadge(0);
                    }
                })
                .catch(error => {
                    console.error('❌ Error loading notifications:', error);
                    // Show empty state on error
                    const notificationList = document.getElementById('notification-list');
                    const emptyNotifications = document.getElementById('empty-notifications');
                    if (notificationList && emptyNotifications) {
                        console.log('📭 Showing empty state due to error');
                        notificationList.innerHTML = '';
                        emptyNotifications.style.display = 'block';
                        updateNotificationBadge(0);
                    }
                });
        }

        // Function to handle accepting or rejecting an application (matching client_page.html)
        function handleApplication(applicationId, action) {
            fetch(`/handle-application/${applicationId}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Find the notification item and update its status immediately in the UI
                    const notificationItem = document.querySelector(`.notification-item[data-id="${applicationId}"]`);
                    if (notificationItem) {
                        // Remove unread class
                        notificationItem.classList.remove('unread');

                        // Remove action buttons
                        const actionsDiv = notificationItem.querySelector('.notification-actions');
                        if (actionsDiv) {
                            actionsDiv.remove();
                        }

                        // Add status indicator
                        const contentDiv = notificationItem.querySelector('.notification-content');
                        if (contentDiv) {
                            const statusDiv = document.createElement('div');
                            statusDiv.className = `notification-status ${action === 'accept' ? 'accepted' : 'rejected'}`;

                            if (action === 'accept') {
                                statusDiv.innerHTML = '<i class="fas fa-check-circle" style="margin-right: 4px;"></i> Accepted';
                            } else {
                                statusDiv.innerHTML = '<i class="fas fa-times-circle" style="margin-right: 4px;"></i> Declined';
                            }

                            contentDiv.appendChild(statusDiv);
                        }
                    }

                    // Update notification count using new badge system
                    decrementNotificationBadge();

                    // Show success message
                    const message = action === 'accept' ?
                        'Application accepted! A welcome message has been sent to the genius.' :
                        'Application declined.';

                    console.log('✅', message);

                    // Optionally reload all notifications after a short delay
                    setTimeout(() => {
                        loadNotifications();
                    }, 2000);
                } else {
                    console.error('Error handling application:', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        function createNotificationItem(notification) {
            const item = document.createElement('div');
            item.className = 'notification-item';

            // Check for both accepted and declined status variations
            const isAccepted = notification.status === 'accepted' || notification.status === 'accept';
            const isDeclined = notification.status === 'declined' || notification.status === 'reject' || notification.status === 'decline';

            const statusIcon = isAccepted ?
                '<i class="fas fa-check-circle" style="color: #2ecc71;"></i>' :
                '<i class="fas fa-times-circle" style="color: #e74c3c;"></i>';

            const statusText = isAccepted ? 'Accepted' :
                              isDeclined ? 'Declined' :
                              notification.status; // fallback to actual status

            const timeAgo = formatTimeAgo(notification.updated_at || notification.created_at);

            // Build client information for the title line
            let clientNameText = '';
            if (notification.client_first_name && notification.client_last_name) {
                const clientName = `${notification.client_first_name} ${notification.client_last_name}`;
                const companyName = notification.business_name || notification.company_name;

                if (companyName && companyName !== clientName) {
                    clientNameText = ` by ${clientName} at ${companyName}`;
                } else {
                    clientNameText = ` by ${clientName}`;
                }
            } else if (notification.business_name || notification.company_name) {
                const companyName = notification.business_name || notification.company_name;
                clientNameText = ` by ${companyName}`;
            }

            item.innerHTML = `
                <div class="notification-content">
                    <div class="notification-header">
                        ${statusIcon}
                        <span class="notification-title">Application ${statusText}${clientNameText}</span>
                    </div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
            `;

            return item;
        }

        function formatTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        // Function to request notification permissions
        function requestNotificationPermission() {
            if ("Notification" in window) {
                if (Notification.permission !== "granted" && Notification.permission !== "denied") {
                    Notification.requestPermission().then(permission => {
                        if (permission === "granted") {
                            console.log("Notification permission granted");
                            // Show a test notification
                            const notification = new Notification("Notifications Enabled", {
                                body: "You will now receive notifications for new messages",
                                icon: '/static/img/logo.png'
                            });
                            setTimeout(() => notification.close(), 5000);
                        }
                    });
                }
            }
        }

        // Profile dropdown and mobile functionality
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Initialize file upload functionality
                setupFileUpload();

                // Enable file upload button in message input when a contact is selected
                const messageFileUploadBtn = document.getElementById('message-file-upload-btn');

                // Initialize notification system
                initializeNotificationSystem();

                // Initialize real-time notifications for client users
                const userType = '{{ session.get("user_type", "") }}';
                if (userType === 'client') {
                    console.log('🔄 Initializing real-time notifications for client user');
                    initializeRealTimeNotifications();
                }

                // Load notifications once on page load
                setTimeout(() => {
                    console.log('🔄 Loading notifications on page load...');
                    loadNotifications();
                }, 2000);

                // Request notification permission for new messages
                requestNotificationPermission();

                // Update file upload button state when a contact is selected
                const originalSelectContact = selectContact;
                selectContact = function(contactId, contactName, contactPhoto) {
                    originalSelectContact(contactId, contactName, contactPhoto);

                    // Enable file upload button in message input
                    const messageFileUploadBtn = document.getElementById('message-file-upload-btn');
                    if (messageFileUploadBtn) {
                        messageFileUploadBtn.disabled = false;
                    }
                };
            } catch (error) {
                console.error('Error setting up file upload:', error);
            }

            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });

            // Mobile contacts toggle
            const toggleContactsBtn = document.getElementById('toggle-contacts-btn');
            const contactsList = document.getElementById('contacts-list');

            if (toggleContactsBtn && contactsList) {
                toggleContactsBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    contactsList.classList.toggle('active');
                });

                // Close contacts when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 992 &&
                        !contactsList.contains(e.target) &&
                        e.target !== toggleContactsBtn) {
                        contactsList.classList.remove('active');
                    }
                });
            }

            // Add event delegation for contact items
            document.getElementById('contacts-container').addEventListener('click', function(e) {
                // Find the closest contact-item parent
                const contactItem = e.target.closest('.contact-item');
                if (contactItem && window.innerWidth <= 992) {
                    contactsList.classList.remove('active');
                }
            });

            // Mobile navigation toggle
            const mobileNavToggle = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');
            const navDropdowns = document.querySelectorAll('.nav-dropdown');

            if (mobileNavToggle && navLinks) {
                mobileNavToggle.addEventListener('click', function(e) {
                    e.stopPropagation();
                    navLinks.classList.toggle('active');
                });

                // Handle dropdown toggles on mobile
                navDropdowns.forEach(dropdown => {
                    const dropBtn = dropdown.querySelector('.nav-dropbtn');
                    if (dropBtn) {
                        dropBtn.addEventListener('click', function(e) {
                            if (window.innerWidth <= 992) {
                                e.preventDefault();
                                e.stopPropagation();
                                dropdown.classList.toggle('active');
                            }
                        });
                    }
                });

                // Close mobile nav when clicking outside
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 992 &&
                        !navLinks.contains(e.target) &&
                        e.target !== mobileNavToggle) {
                        navLinks.classList.remove('active');

                        // Also close any open dropdowns
                        navDropdowns.forEach(dropdown => {
                            dropdown.classList.remove('active');
                        });
                    }
                });
            }
        });

        // Real-time notification functions for client users
        function initializeRealTimeNotifications() {
            console.log('🔄 Setting up real-time notifications...');

            // Use the existing Socket.IO connection from chat functionality
            if (typeof socket !== 'undefined' && socket) {
                console.log('✅ Using existing Socket.IO connection for notifications');
                setupNotificationListeners(socket);
            } else {
                console.log('⚠️ No existing socket found, creating new connection for notifications');
                // Create a new Socket.IO connection specifically for notifications
                const notificationSocket = io({
                    path: '/socket.io',
                    transports: ['polling', 'websocket'],
                    query: {
                        user_id: {{ session.get('user_id', 0) }},
                        user_type: 'client'
                    }
                });

                notificationSocket.on('connect', () => {
                    console.log('🔗 Notification socket connected');
                    notificationSocket.emit('join', {
                        user_id: {{ session.get('user_id', 0) }},
                        user_type: 'client'
                    });
                });

                setupNotificationListeners(notificationSocket);
            }
        }

        function setupNotificationListeners(socketConnection) {
            console.log('👂 Setting up notification event listeners');

            // Listen for new job applications
            socketConnection.on('new_job_application', function(data) {
                console.log('🔔 New job application received:', data);

                // Show real-time notification popup
                showRealTimeNotification(data);

                // Add to notification dropdown list
                addNotificationToDropdown(data);

                // Update notification badge count
                incrementNotificationBadge();

                // Show browser notification if permission granted
                showBrowserNotification(data);
            });
        }

        function showRealTimeNotification(data) {
            // Show a toast notification for new applications
            console.log('📢 Showing real-time notification for:', data.genius_name);

            // Create a temporary notification element
            const notification = document.createElement('div');
            notification.className = 'real-time-notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
                color: white;
                padding: 16px 20px;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                max-width: 350px;
                animation: slideInRight 0.3s ease-out;
                cursor: pointer;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-user-plus" style="font-size: 20px;"></i>
                    <div>
                        <div style="font-weight: 600; margin-bottom: 4px;">New Job Application</div>
                        <div style="font-size: 14px; opacity: 0.9;">${data.genius_name} applied for "${data.job_title}"</div>
                    </div>
                </div>
            `;

            // Add click handler to open allgigpost
            notification.addEventListener('click', () => {
                localStorage.setItem('openProposal', JSON.stringify({
                    jobId: data.job_id,
                    jobTitle: data.job_title,
                    applicationId: data.application_id
                }));
                window.location.href = '/allgigpost';
            });

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        function addNotificationToDropdown(data) {
            console.log('📝 Adding notification to dropdown:', data);

            const notificationList = document.getElementById('notification-list');
            const emptyNotifications = document.getElementById('empty-notifications');

            // Hide empty state if visible
            if (emptyNotifications && emptyNotifications.style.display === 'block') {
                emptyNotifications.style.display = 'none';
            }

            // Create new notification item
            const notificationItem = document.createElement('div');
            notificationItem.className = 'notification-item unread';
            notificationItem.setAttribute('data-id', data.application_id);

            const notificationContent = `
                <div class="notification-icon-wrapper">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="notification-content">
                    <p><strong>${data.genius_name}</strong> applied for your job: <strong>${data.job_title}</strong></p>
                    <span>Just now</span>
                    <div class="notification-actions">
                        <button class="accept-btn" data-id="${data.application_id}">
                            <i class="fas fa-check" style="margin-right: 5px;"></i> Accept
                        </button>
                        <button class="reject-btn" data-id="${data.application_id}">
                            <i class="fas fa-times" style="margin-right: 5px;"></i> Decline
                        </button>
                    </div>
                </div>
            `;

            notificationItem.innerHTML = notificationContent;

            // Add click event to view application details
            notificationItem.addEventListener('click', function(e) {
                if (e.target.closest('.notification-actions')) {
                    return;
                }

                localStorage.setItem('openProposal', JSON.stringify({
                    jobId: data.job_id,
                    jobTitle: data.job_title,
                    applicationId: data.application_id
                }));

                window.location.href = '/allgigpost';
            });

            // Add to the top of the notification list
            if (notificationList) {
                notificationList.insertBefore(notificationItem, notificationList.firstChild);
            }

            // Add event listeners for new accept/reject buttons
            const acceptBtn = notificationItem.querySelector('.accept-btn');
            const rejectBtn = notificationItem.querySelector('.reject-btn');

            if (acceptBtn) {
                acceptBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const applicationId = this.getAttribute('data-id');
                    handleApplication(applicationId, 'accept');
                });
            }

            if (rejectBtn) {
                rejectBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const applicationId = this.getAttribute('data-id');
                    handleApplication(applicationId, 'reject');
                });
            }
        }

        function showBrowserNotification(data) {
            if ("Notification" in window && Notification.permission === "granted") {
                console.log('🔔 Showing browser notification');

                const notification = new Notification("New Job Application", {
                    body: `${data.genius_name} applied for your job: ${data.job_title}`,
                    icon: '/static/img/logo.png',
                    tag: `application-${data.application_id}`
                });

                // Auto close after 5 seconds
                setTimeout(() => notification.close(), 5000);

                // Handle click to open application
                notification.onclick = function() {
                    window.focus();

                    localStorage.setItem('openProposal', JSON.stringify({
                        jobId: data.job_id,
                        jobTitle: data.job_title,
                        applicationId: data.application_id
                    }));

                    window.location.href = '/allgigpost';
                    notification.close();
                };
            }
        }

        // Chat Info Sidebar Functions
        function toggleChatInfo() {
            const sidebar = document.getElementById('chat-info-sidebar');
            sidebar.classList.toggle('active');
        }

        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const header = content.previousElementSibling;
            const icon = header.querySelector('i.fa-chevron-down, i.fa-chevron-up');

            content.classList.toggle('active');

            if (content.classList.contains('active')) {
                icon.className = 'fas fa-chevron-up';
            } else {
                icon.className = 'fas fa-chevron-down';
            }
        }

        function viewProfile() {
            alert('View Profile functionality - to be implemented');
        }

        function muteChat() {
            alert('Mute Chat functionality - to be implemented');
        }

        function searchInChat() {
            alert('Search in Chat functionality - to be implemented');
        }

        // Update chat info when a conversation is selected
        function updateChatInfo(contact) {
            if (contact) {
                const chatInfoName = document.getElementById('chat-info-name');
                const chatInfoAvatar = document.getElementById('chat-info-avatar');

                if (chatInfoName) {
                    chatInfoName.textContent = contact.name || 'Contact Name';
                }

                if (chatInfoAvatar) {
                    // Set the profile photo with error handling
                    const profilePhotoUrl = contact.profile_photo || "{{ url_for('static', filename='img/default-avatar.png') }}";

                    chatInfoAvatar.onerror = function() {
                        console.log('Profile photo failed to load, using default avatar');
                        this.src = "{{ url_for('static', filename='img/default-avatar.png') }}";
                        this.onerror = null; // Prevent infinite loop
                    };

                    chatInfoAvatar.src = profilePhotoUrl;
                    console.log('Updated chat info avatar to:', profilePhotoUrl);
                }
            }
        }

        // Close sidebar when clicking outside
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('chat-info-sidebar');
            const infoBtn = document.getElementById('chat-info-btn');

            if (sidebar.classList.contains('active') &&
                !sidebar.contains(event.target) &&
                !infoBtn.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });

        // Add CSS animations for real-time notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
